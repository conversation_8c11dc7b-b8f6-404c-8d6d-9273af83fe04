<?php
// ไฟล์ debug สำหรับตรวจสอบปัญหา
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Sweet Cabal Debug Information</h1>";

echo "<h2>1. PHP Information</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";

echo "<h2>2. File Existence Check</h2>";
$files_to_check = [
    'config/database.php',
    'classes/Game.php',
    'classes/User.php',
    'includes/header.php',
    'includes/footer.php',
    'css/main.css',
    'css/game-style.css',
    'js/main.js',
    'js/game-functions.js'
];

foreach ($files_to_check as $file) {
    echo $file . ": " . (file_exists($file) ? "✅ มีไฟล์" : "❌ ไม่มีไฟล์") . "<br>";
}

echo "<h2>3. Database Connection Test</h2>";
try {
    require_once 'config/database.php';
    echo "Database config loaded successfully<br>";
    
    $db = new Database();
    echo "Database class instantiated<br>";
    
    $conn = $db->getConnection();
    if ($conn) {
        echo "✅ Database connection successful<br>";
        $db->close();
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<h2>4. Game Class Test</h2>";
try {
    require_once 'classes/Game.php';
    echo "Game class loaded successfully<br>";
    
    $game = new Game();
    echo "Game class instantiated<br>";
    
    $servers = $game->getAllServers();
    echo "Servers count: " . count($servers) . "<br>";
    
    $news = $game->getNews(3);
    echo "News count: " . count($news) . "<br>";
    
} catch (Exception $e) {
    echo "❌ Game class error: " . $e->getMessage() . "<br>";
}

echo "<h2>5. Include Test</h2>";
ob_start();
try {
    $page_title = "Debug Test";
    require_once 'includes/header.php';
    echo "Header included successfully<br>";
} catch (Exception $e) {
    echo "❌ Header include error: " . $e->getMessage() . "<br>";
}
ob_end_clean();

echo "<h2>6. CSS/JS Files</h2>";
$css_files = ['css/main.css', 'css/game-style.css'];
$js_files = ['js/main.js', 'js/game-functions.js'];

foreach ($css_files as $file) {
    if (file_exists($file)) {
        echo $file . ": " . filesize($file) . " bytes<br>";
    }
}

foreach ($js_files as $file) {
    if (file_exists($file)) {
        echo $file . ": " . filesize($file) . " bytes<br>";
    }
}

echo "<h2>7. Error Log</h2>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    echo "Error log location: " . $error_log . "<br>";
    $errors = file_get_contents($error_log);
    $recent_errors = array_slice(explode("\n", $errors), -10);
    echo "<pre>" . implode("\n", $recent_errors) . "</pre>";
} else {
    echo "No error log found or configured<br>";
}

echo "<h2>8. Recommendations</h2>";
echo "1. ตรวจสอบว่าไฟล์ทั้งหมดอัพโหลดครบ<br>";
echo "2. ตรวจสอบการเชื่อมต่อฐานข้อมูล<br>";
echo "3. รันสคริปต์สร้างตารางฐานข้อมูล<br>";
echo "4. ตรวจสอบ PHP extensions (sqlsrv, pdo_sqlsrv)<br>";
echo "5. ตรวจสอบ file permissions<br>";

echo "<hr>";
echo "<a href='test_db.php'>ทดสอบฐานข้อมูล</a> | ";
echo "<a href='index.php'>กลับหน้าหลัก</a>";
?>
