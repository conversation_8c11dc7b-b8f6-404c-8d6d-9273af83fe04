// Main JavaScript file for Sweet Cabal website

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeModals();
    initializeScrollEffects();
    updateServerStatus();
    
    // Update server status every 30 seconds
    setInterval(updateServerStatus, 30000);
});

// Navigation functionality
function initializeNavigation() {
    const navbar = document.querySelector('.navbar');
    const navLinks = document.querySelectorAll('.nav-link');
    
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    
    // Active link highlighting
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            if (href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
    
    // Update active nav link on scroll
    window.addEventListener('scroll', updateActiveNavLink);
}

// Update active navigation link based on scroll position
function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        if (window.scrollY >= sectionTop - 200) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === '#' + current) {
            link.classList.add('active');
        }
    });
}

// Modal functionality
function initializeModals() {
    const modals = document.querySelectorAll('.modal');
    
    // Close modal when clicking outside
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal(modal.id);
            }
        });
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal.id);
            }
        }
    });
}

// Initialize scroll effects
function initializeScrollEffects() {
    // Parallax effect for hero section
    const hero = document.querySelector('.hero-section');
    if (hero) {
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            hero.style.transform = `translateY(${rate}px)`;
        });
    }
}

// Update server status
async function updateServerStatus() {
    try {
        const response = await fetch('api/server-status.php');
        const servers = await response.json();
        
        servers.forEach(server => {
            updateServerCard(server);
        });
        
    } catch (error) {
        console.error('Failed to update server status:', error);
    }
}

// Update individual server card
function updateServerCard(server) {
    const serverCard = document.querySelector(`[data-server-id="${server.server_id}"]`);
    if (!serverCard) return;
    
    const statusElement = serverCard.querySelector('.server-status');
    const playersElement = serverCard.querySelector('.server-players');
    const progressBar = serverCard.querySelector('.progress-bar');
    
    if (statusElement) {
        statusElement.textContent = getServerStatusText(server.server_status);
        statusElement.className = `server-status ${getServerStatusClass(server.server_status)}`;
    }
    
    if (playersElement && server.server_status === 'online') {
        playersElement.textContent = `${server.current_players} / ${server.max_players}`;
    }
    
    if (progressBar && server.server_status === 'online') {
        const percentage = (server.current_players / server.max_players) * 100;
        progressBar.style.width = `${percentage}%`;
    }
}

// Utility functions
function getServerStatusText(status) {
    switch (status) {
        case 'online': return 'ออนไลน์';
        case 'maintenance': return 'ปรับปรุง';
        case 'offline': return 'ออฟไลน์';
        default: return 'ไม่ทราบสถานะ';
    }
}

function getServerStatusClass(status) {
    switch (status) {
        case 'online': return 'server-online';
        case 'maintenance': return 'server-maintenance';
        case 'offline': return 'server-offline';
        default: return '';
    }
}

// Smooth scroll to section
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}
