<?php
// Database Configuration for SQL Server
define('DB_HOST', 'localhost');
define('DB_NAME', 'sweetcabal_db');
define('DB_USER', 'sa');
define('DB_PASS', 'mataPassWdcw51EWxc');
define('DB_PORT', '1433');

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $port = DB_PORT;
    public $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            // SQL Server connection using sqlsrv driver
            $connectionInfo = array(
                "Database" => $this->db_name,
                "UID" => $this->username,
                "PWD" => $this->password,
                "CharacterSet" => "UTF-8"
            );

            $serverName = $this->host . "," . $this->port;
            $this->conn = sqlsrv_connect($serverName, $connectionInfo);

            if ($this->conn === false) {
                throw new Exception("Connection failed: " . print_r(sqlsrv_errors(), true));
            }

        } catch(Exception $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        return $this->conn;
    }

    // Alternative PDO connection method for SQL Server
    public function getPDOConnection() {
        try {
            $dsn = "sqlsrv:Server=" . $this->host . "," . $this->port . ";Database=" . $this->db_name;
            $pdo = new PDO($dsn, $this->username, $this->password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            return $pdo;
        } catch(PDOException $exception) {
            echo "PDO Connection error: " . $exception->getMessage();
            return null;
        }
    }

    // Helper method to execute queries with sqlsrv
    public function query($sql, $params = array()) {
        if ($this->conn === null) {
            $this->getConnection();
        }

        $stmt = sqlsrv_query($this->conn, $sql, $params);
        if ($stmt === false) {
            throw new Exception("Query failed: " . print_r(sqlsrv_errors(), true));
        }

        return $stmt;
    }

    // Helper method to fetch all results
    public function fetchAll($stmt) {
        $results = array();
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $results[] = $row;
        }
        return $results;
    }

    // Helper method to fetch single result
    public function fetch($stmt) {
        return sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC);
    }

    // Close connection
    public function close() {
        if ($this->conn) {
            sqlsrv_close($this->conn);
        }
    }
}

// Site Configuration
define('SITE_NAME', 'Sweet Cabal');
define('SITE_URL', 'http://localhost/sweetcabal');
define('SITE_DESCRIPTION', 'เกม MMORPG สุดมันส์ที่จะพาคุณไปสู่โลกแห่งการผจญภัย');

// Security
define('HASH_ALGO', PASSWORD_DEFAULT);
define('SESSION_LIFETIME', 3600); // 1 hour

// File Upload
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5242880); // 5MB

// Game Settings
define('MAX_LEVEL', 999);
define('STARTING_LEVEL', 1);
define('STARTING_EXP', 0);

// Server Status
define('SERVER_STATUS_ONLINE', 'online');
define('SERVER_STATUS_MAINTENANCE', 'maintenance');
define('SERVER_STATUS_OFFLINE', 'offline');
?>
