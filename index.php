<?php
ob_start();
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load constants
require_once 'config/constants.php';

$page_title = 'หน้าหลัก';
$page_description = SITE_DESCRIPTION;

// ข้อมูล fallback ในกรณีที่ฐานข้อมูลไม่พร้อม
$servers = [];
$featuredNews = [];
$latestNews = [];
$activeEvents = [];
$downloads = [];

try {
    require_once 'classes/Game.php';
    $game = new Game();

    // ดึงข้อมูลเซิร์ฟเวอร์
    $servers = $game->getAllServers();

    // ดึงข้อมูลข่าวสาร
    $featuredNews = $game->getNews(3, null, true);
    $latestNews = $game->getNews(6);

    // ดึงข้อมูลกิจกรรม
    $activeEvents = $game->getEvents('active', 3);

    // ดึงข้อมูลการดาวน์โหลด
    $downloads = $game->getDownloads();

} catch (Exception $e) {
    // ใช้ข้อมูล fallback หากเกิดข้อผิดพลาด
    error_log("Database error in index.php: " . $e->getMessage());

    // ข้อมูลเซิร์ฟเวอร์ fallback
    $servers = [
        [
            'server_id' => 1,
            'server_name' => 'Sweet Server',
            'server_description' => 'เซิร์ฟเวอร์หลักสำหรับผู้เล่นทั่วไป',
            'server_status' => 'online',
            'current_players' => 1234,
            'max_players' => 2000
        ],
        [
            'server_id' => 2,
            'server_name' => 'Cabal Server',
            'server_description' => 'เซิร์ฟเวอร์สำหรับผู้เล่นระดับสูง',
            'server_status' => 'online',
            'current_players' => 856,
            'max_players' => 2000
        ],
        [
            'server_id' => 3,
            'server_name' => 'Magic Server',
            'server_description' => 'เซิร์ฟเวอร์ทดสอบฟีเจอร์ใหม่',
            'server_status' => 'maintenance',
            'current_players' => 0,
            'max_players' => 1000
        ]
    ];

    // ข้อมูลข่าวสาร fallback
    $featuredNews = [
        [
            'news_id' => 1,
            'title' => 'อัพเดทใหญ่ Sweet Cabal 2.0',
            'summary' => 'เตรียมพบกับระบบใหม่ที่จะทำให้การผจญภัยของคุณสนุกมากขึ้น พร้อมด้วยตัวละครใหม่และดันเจี้ยนลึกลับ',
            'category' => 'update',
            'featured' => 1,
            'image_url' => 'images/news/update-2-0.jpg',
            'views' => 2547,
            'published_at' => '2024-12-15 10:00:00'
        ],
        [
            'news_id' => 2,
            'title' => 'กิจกรรมวันวาเลนไทน์ 2025',
            'summary' => 'ร่วมกิจกรรมพิเศษรับรางวัลสุดหวาน ไอเทมลิมิเต็ดและคอสตูมสุดน่ารัก',
            'category' => 'event',
            'featured' => 1,
            'image_url' => 'images/news/valentine-event.jpg',
            'views' => 1823,
            'published_at' => '2024-12-10 14:30:00'
        ],
        [
            'news_id' => 3,
            'title' => 'ทัวร์นาเมนต์ PvP ประจำเดือน',
            'summary' => 'แสดงความแข็งแกร่งในสนามรบ รางวัลรวมมูลค่ากว่า 100,000 บาท',
            'category' => 'tournament',
            'featured' => 0,
            'image_url' => 'images/news/pvp-tournament.jpg',
            'views' => 3421,
            'published_at' => '2024-12-05 16:45:00'
        ]
    ];

    // ข้อมูลดาวน์โหลด fallback
    $downloads = [
        [
            'download_id' => 1,
            'platform' => 'windows',
            'version' => '1.2.3',
            'file_size' => 2684354560,
            'download_count' => 15420,
            'is_latest' => 1
        ],
        [
            'download_id' => 2,
            'platform' => 'macos',
            'version' => '1.2.3',
            'file_size' => 2415919104,
            'download_count' => 3247,
            'is_latest' => 1
        ],
        [
            'download_id' => 3,
            'platform' => 'android',
            'version' => '1.2.1',
            'file_size' => 1932735283,
            'download_count' => 8934,
            'is_latest' => 1
        ]
    ];
}

require_once 'includes/header.php';
?>
    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6" data-aos="fade-right">
                    <div class="hero-content">
                        <h1 class="display-3 fw-bold mb-4">Sweet Cabal</h1>
                        <p class="lead mb-4">เกม MMORPG สุดมันส์ที่จะพาคุณไปสู่โลกแห่งการผจญภัย</p>
                        <div class="hero-features mb-4">
                            <div class="row g-3">
                                <div class="col-auto">
                                    <span class="badge bg-primary fs-6 p-2">
                                        <i class="fas fa-sword me-2"></i>PvP สุดมันส์
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <span class="badge bg-warning fs-6 p-2">
                                        <i class="fas fa-users me-2"></i>Guild War
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <span class="badge bg-danger fs-6 p-2">
                                        <i class="fas fa-dragon me-2"></i>Boss Raid
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="hero-actions">
                            <a href="#download" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-play me-2"></i>เล่นเลย
                            </a>
                            <button class="btn btn-outline-light btn-lg" data-bs-toggle="modal" data-bs-target="#trailerModal">
                                <i class="fas fa-video me-2"></i>ดูตัวอย่าง
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    <div class="hero-image text-center">
                        <img src="images/hero-character.png" alt="Sweet Cabal Character" class="img-fluid floating">
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Stats -->
        <div class="container">
            <div class="row text-center" data-aos="fade-up">
                <div class="col-md-4 mb-4">
                    <div class="game-card">
                        <h3 class="text-primary">50,000+</h3>
                        <p class="mb-0">ผู้เล่นออนไลน์</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="game-card">
                        <h3 class="text-warning">1,000+</h3>
                        <p class="mb-0">กิลด์ที่ลงทะเบียน</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="game-card">
                        <h3 class="text-success">24/7</h3>
                        <p class="mb-0">เซิร์ฟเวอร์ออนไลน์</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Server Status Section -->
    <section class="py-5 bg-dark">
        <div class="container">
            <div class="row">
                <div class="col-12" data-aos="fade-up">
                    <h2 class="text-center mb-5">
                        <i class="fas fa-server text-primary me-2"></i>สถานะเซิร์ฟเวอร์
                    </h2>
                </div>
            </div>
            <div class="row g-4">
                <?php foreach ($servers as $server): ?>
                    <?php
                    $statusClass = '';
                    $statusText = '';
                    $statusIcon = '';
                    $percentage = 0;

                    switch ($server['server_status']) {
                        case 'online':
                            $statusClass = 'server-online';
                            $statusText = 'ออนไลน์';
                            $statusIcon = 'fas fa-circle';
                            $percentage = ($server['current_players'] / $server['max_players']) * 100;
                            break;
                        case 'maintenance':
                            $statusClass = 'server-maintenance';
                            $statusText = 'ปรับปรุง';
                            $statusIcon = 'fas fa-tools';
                            break;
                        case 'offline':
                            $statusClass = 'server-offline';
                            $statusText = 'ออฟไลน์';
                            $statusIcon = 'fas fa-times-circle';
                            break;
                    }
                    ?>
                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo array_search($server, $servers) * 100; ?>">
                        <div class="server-status">
                            <div class="d-flex align-items-center mb-3">
                                <div class="server-icon me-3">
                                    <i class="fas fa-server text-primary"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1"><?php echo htmlspecialchars($server['server_name']); ?></h5>
                                    <div class="<?php echo $statusClass; ?>">
                                        <i class="<?php echo $statusIcon; ?> me-1"></i>
                                        <?php echo $statusText; ?>
                                    </div>
                                </div>
                            </div>

                            <?php if ($server['server_status'] === 'online'): ?>
                                <div class="server-population">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>ผู้เล่นออนไลน์</span>
                                        <span><?php echo number_format($server['current_players']); ?> / <?php echo number_format($server['max_players']); ?></span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: <?php echo $percentage; ?>%"></div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <p class="text-muted mb-0"><?php echo htmlspecialchars($server['server_description']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- News Section -->
    <section class="py-5 bg-dark" id="news">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5" data-aos="fade-up">
                    <h2 class="display-5 fw-bold mb-3">ข่าวสารล่าสุด</h2>
                    <p class="lead">ติดตามข่าวสารและกิจกรรมใหม่ๆ ของเกม</p>
                </div>
            </div>

            <div class="row g-4">
                <?php foreach ($featuredNews as $index => $news): ?>
                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                        <div class="news-card h-100">
                            <?php if ($news['image_url']): ?>
                                <img src="<?php echo htmlspecialchars($news['image_url']); ?>"
                                     class="card-img-top"
                                     alt="<?php echo htmlspecialchars($news['title']); ?>">
                            <?php endif; ?>

                            <?php if ($news['featured']): ?>
                                <span class="news-badge badge bg-warning">ข่าวใหญ่</span>
                            <?php endif; ?>

                            <div class="card-body d-flex flex-column">
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo date('d M Y', strtotime($news['published_at'])); ?>
                                    </small>
                                    <span class="badge bg-primary ms-2"><?php echo ucfirst($news['category']); ?></span>
                                </div>

                                <h5 class="card-title text-primary">
                                    <?php echo htmlspecialchars($news['title']); ?>
                                </h5>

                                <p class="card-text flex-grow-1">
                                    <?php echo htmlspecialchars($news['summary']); ?>
                                </p>

                                <div class="d-flex justify-content-between align-items-center mt-auto">
                                    <a href="news-detail.php?id=<?php echo $news['news_id']; ?>"
                                       class="btn btn-outline-primary btn-sm">
                                        อ่านต่อ <i class="fas fa-arrow-right ms-1"></i>
                                    </a>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i><?php echo number_format($news['views']); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="row mt-5">
                <div class="col-12 text-center" data-aos="fade-up">
                    <a href="news.php" class="btn btn-primary btn-lg">
                        ดูข่าวสารทั้งหมด <i class="fas fa-chevron-right ms-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section class="py-5" id="download">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5" data-aos="fade-up">
                    <h2 class="display-5 fw-bold mb-3">ดาวน์โหลดเกม</h2>
                    <p class="lead">เลือกแพลตฟอร์มที่คุณต้องการและเริ่มการผจญภัยได้เลย</p>
                </div>
            </div>

            <div class="row g-4 justify-content-center">
                <?php
                $platformIcons = [
                    'windows' => 'fab fa-windows',
                    'macos' => 'fab fa-apple',
                    'android' => 'fab fa-android',
                    'ios' => 'fab fa-app-store'
                ];

                $platformNames = [
                    'windows' => 'Windows',
                    'macos' => 'macOS',
                    'android' => 'Android',
                    'ios' => 'iOS'
                ];

                $downloadsByPlatform = [];
                foreach ($downloads as $download) {
                    if ($download['is_latest']) {
                        $downloadsByPlatform[$download['platform']] = $download;
                    }
                }

                foreach ($downloadsByPlatform as $platform => $download):
                ?>
                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo array_search($platform, array_keys($downloadsByPlatform)) * 100; ?>">
                        <div class="download-card">
                            <div class="download-icon">
                                <i class="<?php echo $platformIcons[$platform] ?? 'fas fa-download'; ?>"></i>
                            </div>
                            <h4 class="mb-3"><?php echo $platformNames[$platform] ?? ucfirst($platform); ?></h4>
                            <p class="text-muted mb-3">เวอร์ชัน <?php echo htmlspecialchars($download['version']); ?></p>
                            <p class="mb-3">ขนาดไฟล์: <?php echo formatFileSize($download['file_size']); ?></p>
                            <p class="small text-muted mb-4">ดาวน์โหลดแล้ว: <?php echo number_format($download['download_count']); ?> ครั้ง</p>

                            <a href="download-file.php?id=<?php echo $download['download_id']; ?>"
                               class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-download me-2"></i>ดาวน์โหลด
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="row mt-5">
                <div class="col-12 text-center" data-aos="fade-up">
                    <a href="download.php" class="btn btn-outline-primary btn-lg">
                        ดูรายละเอียดเพิ่มเติม <i class="fas fa-chevron-right ms-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

<?php
// Helper function for file size formatting
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    $k = 1024;
    $sizes = array('Bytes', 'KB', 'MB', 'GB', 'TB');
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

require_once 'includes/footer.php';
?>
