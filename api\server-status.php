<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../classes/Game.php';

try {
    $game = new Game();
    $servers = $game->getAllServers();
    
    // ปรับปรุงข้อมูลให้เหมาะสำหรับ JSON response
    $response = [];
    foreach ($servers as $server) {
        $response[] = [
            'server_id' => (int)$server['server_id'],
            'server_name' => $server['server_name'],
            'server_status' => $server['server_status'],
            'current_players' => (int)$server['current_players'],
            'max_players' => (int)$server['max_players'],
            'last_updated' => $server['updated_at']
        ];
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => 'Unable to fetch server status'
    ]);
}
?>
