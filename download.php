<?php
$page_title = 'ดาวน์โหลดเกม';
$page_description = 'ดาวน์โหลด Sweet Cabal เกม MMORPG สุดมันส์ รองรับทุกแพลตฟอร์ม';

require_once 'classes/Game.php';
$game = new Game();

// ดึงข้อมูลไฟล์ดาวน์โหลด
$downloads = $game->getDownloads();

// จัดกลุ่มตามแพลตฟอร์ม
$downloadsByPlatform = [];
foreach ($downloads as $download) {
    $downloadsByPlatform[$download['platform']][] = $download;
}

require_once 'includes/header.php';
?>

<!-- Download Section -->
<section class="py-5 mt-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5" data-aos="fade-up">
                <h1 class="display-4 fw-bold mb-3">ดาวน์โหลดเกม</h1>
                <p class="lead">เลือกแพลตฟอร์มที่คุณต้องการและเริ่มการผจญภัยได้เลย</p>
            </div>
        </div>

        <!-- System Requirements -->
        <div class="row mb-5">
            <div class="col-12" data-aos="fade-up">
                <div class="game-card">
                    <h3 class="text-primary mb-4">
                        <i class="fas fa-desktop me-2"></i>ความต้องการของระบบ
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-warning">ความต้องการขั้นต่ำ</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-microchip text-primary me-2"></i><strong>CPU:</strong> Intel Core i3-4130 / AMD FX-6300</li>
                                <li><i class="fas fa-memory text-primary me-2"></i><strong>RAM:</strong> 4 GB</li>
                                <li><i class="fas fa-hdd text-primary me-2"></i><strong>Storage:</strong> 15 GB พื้นที่ว่าง</li>
                                <li><i class="fas fa-tv text-primary me-2"></i><strong>Graphics:</strong> DirectX 11 compatible</li>
                                <li><i class="fas fa-wifi text-primary me-2"></i><strong>Network:</strong> การเชื่อมต่ออินเทอร์เน็ต</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-success">ความต้องการที่แนะนำ</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-microchip text-primary me-2"></i><strong>CPU:</strong> Intel Core i5-8400 / AMD Ryzen 5 2600</li>
                                <li><i class="fas fa-memory text-primary me-2"></i><strong>RAM:</strong> 8 GB</li>
                                <li><i class="fas fa-hdd text-primary me-2"></i><strong>Storage:</strong> 25 GB พื้นที่ว่าง (SSD)</li>
                                <li><i class="fas fa-tv text-primary me-2"></i><strong>Graphics:</strong> GTX 1060 / RX 580</li>
                                <li><i class="fas fa-wifi text-primary me-2"></i><strong>Network:</strong> Broadband Internet</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Download Cards -->
        <div class="row g-4">
            <?php if (isset($downloadsByPlatform['windows'])): ?>
                <div class="col-lg-4 col-md-6" data-aos="fade-up">
                    <div class="download-card">
                        <div class="download-icon">
                            <i class="fab fa-windows"></i>
                        </div>
                        <h4 class="mb-3">Windows</h4>
                        <?php $latest = $downloadsByPlatform['windows'][0]; ?>
                        <p class="text-muted mb-3">เวอร์ชัน <?php echo htmlspecialchars($latest['version']); ?></p>
                        <p class="mb-3">ขนาดไฟล์: <?php echo formatFileSize($latest['file_size']); ?></p>
                        <p class="small text-muted mb-4">ดาวน์โหลดแล้ว: <?php echo number_format($latest['download_count']); ?> ครั้ง</p>
                        
                        <a href="download-file.php?id=<?php echo $latest['download_id']; ?>" 
                           class="btn btn-primary btn-lg w-100 mb-3">
                            <i class="fas fa-download me-2"></i>ดาวน์โหลด
                        </a>
                        
                        <?php if (!empty($latest['changelog'])): ?>
                            <button class="btn btn-outline-light btn-sm w-100" 
                                    data-bs-toggle="collapse" 
                                    data-bs-target="#changelog-windows">
                                <i class="fas fa-list me-2"></i>ดูรายการอัพเดท
                            </button>
                            <div class="collapse mt-3" id="changelog-windows">
                                <div class="card bg-dark border-secondary">
                                    <div class="card-body">
                                        <h6 class="card-title">รายการอัพเดท v<?php echo htmlspecialchars($latest['version']); ?></h6>
                                        <p class="card-text small"><?php echo nl2br(htmlspecialchars($latest['changelog'])); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (isset($downloadsByPlatform['macos'])): ?>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="download-card">
                        <div class="download-icon">
                            <i class="fab fa-apple"></i>
                        </div>
                        <h4 class="mb-3">macOS</h4>
                        <?php $latest = $downloadsByPlatform['macos'][0]; ?>
                        <p class="text-muted mb-3">เวอร์ชัน <?php echo htmlspecialchars($latest['version']); ?></p>
                        <p class="mb-3">ขนาดไฟล์: <?php echo formatFileSize($latest['file_size']); ?></p>
                        <p class="small text-muted mb-4">ดาวน์โหลดแล้ว: <?php echo number_format($latest['download_count']); ?> ครั้ง</p>
                        
                        <a href="download-file.php?id=<?php echo $latest['download_id']; ?>" 
                           class="btn btn-primary btn-lg w-100 mb-3">
                            <i class="fas fa-download me-2"></i>ดาวน์โหลด
                        </a>
                        
                        <?php if (!empty($latest['changelog'])): ?>
                            <button class="btn btn-outline-light btn-sm w-100" 
                                    data-bs-toggle="collapse" 
                                    data-bs-target="#changelog-macos">
                                <i class="fas fa-list me-2"></i>ดูรายการอัพเดท
                            </button>
                            <div class="collapse mt-3" id="changelog-macos">
                                <div class="card bg-dark border-secondary">
                                    <div class="card-body">
                                        <h6 class="card-title">รายการอัพเดท v<?php echo htmlspecialchars($latest['version']); ?></h6>
                                        <p class="card-text small"><?php echo nl2br(htmlspecialchars($latest['changelog'])); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (isset($downloadsByPlatform['android'])): ?>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="download-card">
                        <div class="download-icon">
                            <i class="fab fa-android"></i>
                        </div>
                        <h4 class="mb-3">Android</h4>
                        <?php $latest = $downloadsByPlatform['android'][0]; ?>
                        <p class="text-muted mb-3">เวอร์ชัน <?php echo htmlspecialchars($latest['version']); ?></p>
                        <p class="mb-3">ขนาดไฟล์: <?php echo formatFileSize($latest['file_size']); ?></p>
                        <p class="small text-muted mb-4">ดาวน์โหลดแล้ว: <?php echo number_format($latest['download_count']); ?> ครั้ง</p>
                        
                        <a href="download-file.php?id=<?php echo $latest['download_id']; ?>" 
                           class="btn btn-primary btn-lg w-100 mb-3">
                            <i class="fas fa-download me-2"></i>ดาวน์โหลด
                        </a>
                        
                        <div class="d-flex gap-2 mb-3">
                            <a href="#" class="btn btn-outline-light btn-sm flex-fill">
                                <i class="fab fa-google-play me-1"></i>Google Play
                            </a>
                            <a href="#" class="btn btn-outline-light btn-sm flex-fill">
                                <i class="fas fa-mobile-alt me-1"></i>APK
                            </a>
                        </div>
                        
                        <?php if (!empty($latest['changelog'])): ?>
                            <button class="btn btn-outline-light btn-sm w-100" 
                                    data-bs-toggle="collapse" 
                                    data-bs-target="#changelog-android">
                                <i class="fas fa-list me-2"></i>ดูรายการอัพเดท
                            </button>
                            <div class="collapse mt-3" id="changelog-android">
                                <div class="card bg-dark border-secondary">
                                    <div class="card-body">
                                        <h6 class="card-title">รายการอัพเดท v<?php echo htmlspecialchars($latest['version']); ?></h6>
                                        <p class="card-text small"><?php echo nl2br(htmlspecialchars($latest['changelog'])); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Installation Guide -->
        <div class="row mt-5">
            <div class="col-12" data-aos="fade-up">
                <div class="game-card">
                    <h3 class="text-primary mb-4">
                        <i class="fas fa-book me-2"></i>คู่มือการติดตั้ง
                    </h3>
                    <div class="row">
                        <div class="col-md-4">
                            <h5 class="text-warning">Windows</h5>
                            <ol class="small">
                                <li>ดาวน์โหลดไฟล์ .exe</li>
                                <li>คลิกขวาและเลือก "Run as administrator"</li>
                                <li>ทำตามขั้นตอนการติดตั้ง</li>
                                <li>เปิดเกมและสร้างบัญชี</li>
                                <li>เริ่มการผจญภัย!</li>
                            </ol>
                        </div>
                        <div class="col-md-4">
                            <h5 class="text-warning">macOS</h5>
                            <ol class="small">
                                <li>ดาวน์โหลดไฟล์ .dmg</li>
                                <li>เปิดไฟล์และลากไปยัง Applications</li>
                                <li>เปิดจาก Applications folder</li>
                                <li>อนุญาตการเข้าถึงหากมีการแจ้งเตือน</li>
                                <li>เริ่มการผจญภัย!</li>
                            </ol>
                        </div>
                        <div class="col-md-4">
                            <h5 class="text-warning">Android</h5>
                            <ol class="small">
                                <li>ดาวน์โหลดจาก Google Play หรือ APK</li>
                                <li>หากเป็น APK ให้เปิดใช้งาน "Unknown sources"</li>
                                <li>ติดตั้งแอปพลิเคชัน</li>
                                <li>เปิดแอปและสร้างบัญชี</li>
                                <li>เริ่มการผจญภัย!</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Support -->
        <div class="row mt-5">
            <div class="col-12 text-center" data-aos="fade-up">
                <div class="game-card">
                    <h4 class="text-primary mb-3">ต้องการความช่วยเหลือ?</h4>
                    <p class="mb-4">หากพบปัญหาในการดาวน์โหลดหรือติดตั้ง สามารถติดต่อทีมสนับสนุนได้</p>
                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                        <a href="support.php" class="btn btn-outline-primary">
                            <i class="fas fa-headset me-2"></i>ติดต่อสนับสนุน
                        </a>
                        <a href="faq.php" class="btn btn-outline-primary">
                            <i class="fas fa-question-circle me-2"></i>คำถามที่พบบ่อย
                        </a>
                        <a href="#" class="btn btn-outline-primary">
                            <i class="fab fa-discord me-2"></i>Discord
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Helper function for file size formatting
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    $k = 1024;
    $sizes = array('Bytes', 'KB', 'MB', 'GB', 'TB');
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

require_once 'includes/footer.php';
?>
