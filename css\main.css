/* Sweet Cabal Website Main Styles */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap');

/* CSS Variables */
:root {
    --primary-color: #00d4ff;
    --secondary-color: #ffd700;
    --accent-color: #8a2be2;
    --danger-color: #ff4444;
    --success-color: #00ff88;
    --warning-color: #ffaa00;
    
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    
    --border-color: #333333;
    --shadow-color: rgba(0, 0, 0, 0.5);
    
    --glow-blue: rgba(0, 212, 255, 0.3);
    --glow-gold: rgba(255, 215, 0, 0.3);
    
    --font-family: 'Kanit', sans-serif;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Background Effects */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, var(--glow-blue) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, var(--glow-gold) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(138, 43, 226, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3rem;
    font-weight: 700;
}

h2 {
    font-size: 2.5rem;
}

h3 {
    font-size: 2rem;
}

p {
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--secondary-color);
}

/* Utility Classes */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }

.bg-primary { background-color: var(--bg-primary) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-tertiary { background-color: var(--bg-tertiary) !important; }

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-block;
    text-align: center;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #0099cc);
    color: white;
    box-shadow: 0 4px 15px var(--glow-blue);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--glow-blue);
    color: white;
}

.btn-secondary {
    background: linear-gradient(45deg, var(--secondary-color), #e6c200);
    color: var(--bg-primary);
    box-shadow: 0 4px 15px var(--glow-gold);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--glow-gold);
    color: var(--bg-primary);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 15px var(--glow-blue);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Cards */
.card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--glow-blue), transparent);
    transition: all 0.5s ease;
}

.card:hover::before {
    left: 100%;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px var(--shadow-color);
    border-color: var(--primary-color);
}

/* Forms */
.form-control {
    background: var(--bg-tertiary) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    background: var(--bg-tertiary) !important;
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem var(--glow-blue) !important;
    color: var(--text-primary) !important;
}

.form-control::placeholder {
    color: var(--text-muted) !important;
}

.form-label {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Navigation */
.navbar {
    background: rgba(10, 10, 10, 0.95) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 20px var(--shadow-color);
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(10, 10, 10, 0.98) !important;
}

.navbar-brand .logo {
    width: 40px;
    height: 40px;
    filter: drop-shadow(0 0 10px var(--glow-blue));
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    text-shadow: 0 0 10px var(--glow-blue);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 80%;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.5)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 30% 70%, var(--glow-blue) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, var(--glow-gold) 0%, transparent 50%);
    opacity: 0.3;
    animation: heroGlow 8s ease-in-out infinite alternate;
}

@keyframes heroGlow {
    0% { opacity: 0.2; }
    100% { opacity: 0.4; }
}

.hero-content h1 {
    font-size: 4rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px var(--glow-blue);
    margin-bottom: 1rem;
}

.hero-content .lead {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.floating {
    animation: float 6s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.pulsing {
    animation: pulse 2s ease-in-out infinite;
}

.fade-in {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.6s ease;
}

.fade-in-element {
    opacity: 0;
    transform: translateY(30px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-content .lead {
        font-size: 1.2rem;
    }
    
    .card {
        margin-bottom: 2rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 1rem;
    }
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0099cc;
}
