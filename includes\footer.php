    <!-- Footer -->
    <footer class="footer bg-dark text-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-brand">
                        <img src="images/Sweet.gif" alt="Sweet Cabal" class="footer-logo mb-3">
                        <h5>Sweet Cabal</h5>
                        <p class="text-muted">
                            เกมผจญภัยสุดหวานที่จะพาคุณไปสู่โลกแห่งความมหัศจรรย์
                            ร่วมผจญภัยไปกับเพื่อนๆ ในโลกที่เต็มไปด้วยความสนุกสนาน
                        </p>
                        <div class="social-links">
                            <a href="#" class="btn btn-outline-light btn-sm me-2">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-outline-light btn-sm me-2">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <a href="#" class="btn btn-outline-light btn-sm me-2">
                                <i class="fab fa-discord"></i>
                            </a>
                            <a href="#" class="btn btn-outline-light btn-sm">
                                <i class="fab fa-line"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-uppercase fw-bold mb-3">เกม</h6>
                    <ul class="list-unstyled">
                        <li><a href="download.php" class="text-muted text-decoration-none">ดาวน์โหลด</a></li>
                        <li><a href="ranking.php" class="text-muted text-decoration-none">จัดอันดับ</a></li>
                        <li><a href="guide.php" class="text-muted text-decoration-none">คู่มือเล่น</a></li>
                        <li><a href="system-requirements.php" class="text-muted text-decoration-none">ความต้องการระบบ</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-uppercase fw-bold mb-3">ชุมชน</h6>
                    <ul class="list-unstyled">
                        <li><a href="news.php" class="text-muted text-decoration-none">ข่าวสาร</a></li>
                        <li><a href="forum.php" class="text-muted text-decoration-none">ฟอรั่ม</a></li>
                        <li><a href="events.php" class="text-muted text-decoration-none">กิจกรรม</a></li>
                        <li><a href="gallery.php" class="text-muted text-decoration-none">แฟนอาร์ต</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-uppercase fw-bold mb-3">สนับสนุน</h6>
                    <ul class="list-unstyled">
                        <li><a href="contact.php" class="text-muted text-decoration-none">ติดต่อเรา</a></li>
                        <li><a href="support.php" class="text-muted text-decoration-none">รายงานปัญหา</a></li>
                        <li><a href="faq.php" class="text-muted text-decoration-none">คำถามที่พบบ่อย</a></li>
                        <li><a href="privacy.php" class="text-muted text-decoration-none">นโยบายความเป็นส่วนตัว</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-uppercase fw-bold mb-3">ติดตามข่าวสาร</h6>
                    <p class="text-muted small">รับข่าวสารและอัพเดทล่าสุด</p>
                    <form class="newsletter-form">
                        <div class="input-group mb-3">
                            <input type="email" class="form-control" placeholder="อีเมลของคุณ">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                    <div class="download-badges">
                        <a href="#" class="d-block mb-2">
                            <img src="images/app-store.png" alt="Download on App Store" class="img-fluid" style="max-height: 40px;">
                        </a>
                        <a href="#" class="d-block">
                            <img src="images/google-play.png" alt="Get it on Google Play" class="img-fluid" style="max-height: 40px;">
                        </a>
                    </div>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 Sweet Cabal. สงวนลิขสิทธิ์ทุกประการ</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="terms.php" class="text-muted text-decoration-none me-3">ข้อกำหนดการใช้งาน</a>
                    <a href="privacy.php" class="text-muted text-decoration-none me-3">นโยบายความเป็นส่วนตัว</a>
                    <a href="cookies.php" class="text-muted text-decoration-none">คุกกี้</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content bg-dark text-light">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title">เข้าสู่ระบบ</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm" action="auth/login.php" method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">ชื่อผู้ใช้:</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">รหัสผ่าน:</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe" name="remember_me">
                            <label class="form-check-label" for="rememberMe">จดจำการเข้าสู่ระบบ</label>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">เข้าสู่ระบบ</button>
                        </div>
                    </form>
                    <div class="text-center mt-3">
                        <a href="#" class="text-decoration-none">ลืมรหัสผ่าน?</a>
                    </div>
                </div>
                <div class="modal-footer border-secondary">
                    <p class="text-muted mb-0">ยังไม่มีบัญชี? 
                        <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#registerModal" data-bs-dismiss="modal">สมัครสมาชิก</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Register Modal -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content bg-dark text-light">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title">สมัครสมาชิก</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm" action="auth/register.php" method="POST">
                        <div class="mb-3">
                            <label for="regUsername" class="form-label">ชื่อผู้ใช้:</label>
                            <input type="text" class="form-control" id="regUsername" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">อีเมล:</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="regPassword" class="form-label">รหัสผ่าน:</label>
                            <input type="password" class="form-control" id="regPassword" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">ยืนยันรหัสผ่าน:</label>
                            <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agreeTerms" name="agree_terms" required>
                            <label class="form-check-label" for="agreeTerms">
                                ฉันยอมรับ <a href="terms.php" class="text-decoration-none" target="_blank">ข้อกำหนดการใช้งาน</a>
                            </label>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">สมัครสมาชิก</button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-secondary">
                    <p class="text-muted mb-0">มีบัญชีแล้ว? 
                        <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#loginModal" data-bs-dismiss="modal">เข้าสู่ระบบ</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Custom JS -->
    <script src="js/main.js"></script>
    <script src="js/game-functions.js"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });
    </script>
</body>
</html>
