<?php
header('Content-Type: application/json');
require_once '../classes/User.php';

// ตรวจสอบว่าเป็น POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // รับข้อมูลจากฟอร์ม
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $agreeTerms = isset($_POST['agree_terms']);
    
    // ตรวจสอบข้อมูลที่จำเป็น
    if (empty($username) || empty($email) || empty($password) || empty($confirmPassword)) {
        echo json_encode(['success' => false, 'message' => 'กรุณากรอกข้อมูลให้ครบถ้วน']);
        exit;
    }
    
    // ตรวจสอบการยอมรับข้อกำหนด
    if (!$agreeTerms) {
        echo json_encode(['success' => false, 'message' => 'กรุณายอมรับข้อกำหนดการใช้งาน']);
        exit;
    }
    
    // ตรวจสอบความยาวของชื่อผู้ใช้
    if (strlen($username) < 3 || strlen($username) > 20) {
        echo json_encode(['success' => false, 'message' => 'ชื่อผู้ใช้ต้องมีความยาว 3-20 ตัวอักษร']);
        exit;
    }
    
    // ตรวจสอบรูปแบบชื่อผู้ใช้ (อนุญาตเฉพาะตัวอักษร ตัวเลข และ underscore)
    if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        echo json_encode(['success' => false, 'message' => 'ชื่อผู้ใช้สามารถใช้ได้เฉพาะตัวอักษร ตัวเลข และ _ เท่านั้น']);
        exit;
    }
    
    // ตรวจสอบรูปแบบอีเมล
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'รูปแบบอีเมลไม่ถูกต้อง']);
        exit;
    }
    
    // ตรวจสอบความยาวของรหัสผ่าน
    if (strlen($password) < 6) {
        echo json_encode(['success' => false, 'message' => 'รหัสผ่านต้องมีความยาวอย่างน้อย 6 ตัวอักษร']);
        exit;
    }
    
    // ตรวจสอบการยืนยันรหัสผ่าน
    if ($password !== $confirmPassword) {
        echo json_encode(['success' => false, 'message' => 'รหัสผ่านและการยืนยันรหัสผ่านไม่ตรงกัน']);
        exit;
    }
    
    // ตรวจสอบความแข็งแกร่งของรหัสผ่าน
    if (!preg_match('/^(?=.*[a-zA-Z])(?=.*\d)/', $password)) {
        echo json_encode(['success' => false, 'message' => 'รหัสผ่านต้องประกอบด้วยตัวอักษรและตัวเลข']);
        exit;
    }
    
    // สร้าง User object และทำการสมัครสมาชิก
    $user = new User();
    $result = $user->register($username, $email, $password);
    
    if ($result['success']) {
        // สมัครสมาชิกสำเร็จ
        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'redirect' => 'index.php'
        ]);
    } else {
        // สมัครสมาชิกไม่สำเร็จ
        echo json_encode([
            'success' => false,
            'message' => $result['message']
        ]);
    }
    
} catch (Exception $e) {
    // จัดการข้อผิดพลาด
    error_log('Registration error: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง'
    ]);
}
?>
