<?php
require_once 'config/database.php';

class Game {
    private $db;
    private $conn;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    // ดึงข้อมูลเซิร์ฟเวอร์ทั้งหมด
    public function getAllServers() {
        try {
            $sql = "SELECT server_id, server_name, server_description, server_status, 
                           max_players, current_players, updated_at 
                    FROM servers 
                    ORDER BY server_id";
            
            $stmt = $this->db->query($sql);
            return $this->db->fetchAll($stmt);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    // ดึงข้อมูลเซิร์ฟเวอร์ตาม ID
    public function getServerById($serverId) {
        try {
            $sql = "SELECT * FROM servers WHERE server_id = ?";
            $stmt = $this->db->query($sql, [$serverId]);
            return $this->db->fetch($stmt);
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    // อัพเดทสถานะเซิร์ฟเวอร์
    public function updateServerStatus($serverId, $status, $currentPlayers = null) {
        try {
            if ($currentPlayers !== null) {
                $sql = "UPDATE servers SET server_status = ?, current_players = ?, updated_at = GETDATE() 
                        WHERE server_id = ?";
                $stmt = $this->db->query($sql, [$status, $currentPlayers, $serverId]);
            } else {
                $sql = "UPDATE servers SET server_status = ?, updated_at = GETDATE() 
                        WHERE server_id = ?";
                $stmt = $this->db->query($sql, [$status, $serverId]);
            }
            
            return $stmt !== false;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    // ดึงข้อมูลตัวละครของผู้ใช้
    public function getUserCharacters($userId) {
        try {
            $sql = "SELECT c.*, s.server_name 
                    FROM characters c 
                    INNER JOIN servers s ON c.server_id = s.server_id 
                    WHERE c.user_id = ? AND c.status = 'active' 
                    ORDER BY c.last_login DESC";
            
            $stmt = $this->db->query($sql, [$userId]);
            return $this->db->fetchAll($stmt);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    // สร้างตัวละครใหม่
    public function createCharacter($userId, $serverId, $characterName, $characterClass) {
        try {
            // ตรวจสอบว่าชื่อตัวละครซ้ำหรือไม่
            if ($this->isCharacterNameExists($characterName, $serverId)) {
                return ['success' => false, 'message' => 'ชื่อตัวละครนี้มีอยู่แล้วในเซิร์ฟเวอร์นี้'];
            }
            
            // ตรวจสอบจำนวนตัวละครสูงสุด
            $maxCharacters = $this->getSystemSetting('max_characters_per_user', 5);
            $currentCharacters = $this->getUserCharacterCount($userId);
            
            if ($currentCharacters >= $maxCharacters) {
                return ['success' => false, 'message' => 'คุณมีตัวละครครบจำนวนสูงสุดแล้ว'];
            }
            
            // สร้างตัวละคร
            $sql = "INSERT INTO characters (user_id, server_id, character_name, character_class) 
                    VALUES (?, ?, ?, ?)";
            
            $stmt = $this->db->query($sql, [$userId, $serverId, $characterName, $characterClass]);
            
            if ($stmt) {
                // สร้างสถิติเริ่มต้น
                $characterId = $this->getLastInsertId();
                $this->createPlayerStats($characterId);
                
                return [
                    'success' => true, 
                    'message' => 'สร้างตัวละครสำเร็จ',
                    'character_id' => $characterId
                ];
            } else {
                return ['success' => false, 'message' => 'เกิดข้อผิดพลาดในการสร้างตัวละคร'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()];
        }
    }
    
    // ตรวจสอบว่าชื่อตัวละครมีอยู่แล้วหรือไม่
    private function isCharacterNameExists($characterName, $serverId) {
        $sql = "SELECT COUNT(*) as count FROM characters 
                WHERE character_name = ? AND server_id = ? AND status = 'active'";
        $stmt = $this->db->query($sql, [$characterName, $serverId]);
        $result = $this->db->fetch($stmt);
        return $result['count'] > 0;
    }
    
    // นับจำนวนตัวละครของผู้ใช้
    private function getUserCharacterCount($userId) {
        $sql = "SELECT COUNT(*) as count FROM characters WHERE user_id = ? AND status = 'active'";
        $stmt = $this->db->query($sql, [$userId]);
        $result = $this->db->fetch($stmt);
        return $result['count'];
    }
    
    // สร้างสถิติเริ่มต้นสำหรับตัวละคร
    private function createPlayerStats($characterId) {
        $sql = "INSERT INTO player_stats (character_id) VALUES (?)";
        $this->db->query($sql, [$characterId]);
    }
    
    // ดึงอันดับผู้เล่น
    public function getPlayerRanking($type = 'level', $serverId = null, $limit = 50) {
        try {
            $orderBy = '';
            switch ($type) {
                case 'level':
                    $orderBy = 'c.level DESC, c.experience DESC';
                    break;
                case 'pvp':
                    $orderBy = 'ps.players_killed DESC, ps.deaths ASC';
                    break;
                default:
                    $orderBy = 'c.level DESC';
            }
            
            $sql = "SELECT TOP (?) c.character_name, c.character_class, c.level, c.experience,
                           s.server_name, ps.players_killed, ps.deaths, ps.monsters_killed,
                           ROW_NUMBER() OVER (ORDER BY {$orderBy}) as rank
                    FROM characters c
                    INNER JOIN servers s ON c.server_id = s.server_id
                    LEFT JOIN player_stats ps ON c.character_id = ps.character_id
                    WHERE c.status = 'active'";
            
            $params = [$limit];
            
            if ($serverId) {
                $sql .= " AND c.server_id = ?";
                $params[] = $serverId;
            }
            
            $sql .= " ORDER BY {$orderBy}";
            
            $stmt = $this->db->query($sql, $params);
            return $this->db->fetchAll($stmt);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    // ดึงอันดับกิลด์
    public function getGuildRanking($serverId = null, $limit = 50) {
        try {
            $sql = "SELECT TOP (?) g.guild_name, g.guild_level, g.guild_experience,
                           g.current_members, s.server_name,
                           c.character_name as guild_master_name,
                           ROW_NUMBER() OVER (ORDER BY g.guild_level DESC, g.guild_experience DESC) as rank
                    FROM guilds g
                    INNER JOIN servers s ON g.server_id = s.server_id
                    INNER JOIN characters c ON g.guild_master_id = c.character_id
                    WHERE g.status = 'active'";
            
            $params = [$limit];
            
            if ($serverId) {
                $sql .= " AND g.server_id = ?";
                $params[] = $serverId;
            }
            
            $sql .= " ORDER BY g.guild_level DESC, g.guild_experience DESC";
            
            $stmt = $this->db->query($sql, $params);
            return $this->db->fetchAll($stmt);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    // ดึงข้อมูลไฟล์ดาวน์โหลด
    public function getDownloads() {
        try {
            $sql = "SELECT platform, version, file_name, file_size, download_url, 
                           changelog, is_latest, download_count, updated_at
                    FROM downloads 
                    WHERE status = 'active' 
                    ORDER BY platform, is_latest DESC, version DESC";
            
            $stmt = $this->db->query($sql);
            return $this->db->fetchAll($stmt);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    // เพิ่มจำนวนการดาวน์โหลด
    public function incrementDownloadCount($downloadId) {
        try {
            $sql = "UPDATE downloads SET download_count = download_count + 1 WHERE download_id = ?";
            $this->db->query($sql, [$downloadId]);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    // ดึงข้อมูลข่าวสาร
    public function getNews($limit = 10, $category = null, $featured = null) {
        try {
            $sql = "SELECT TOP (?) news_id, title, summary, category, featured, 
                           image_url, views, published_at
                    FROM news 
                    WHERE status = 'published'";
            
            $params = [$limit];
            
            if ($category) {
                $sql .= " AND category = ?";
                $params[] = $category;
            }
            
            if ($featured !== null) {
                $sql .= " AND featured = ?";
                $params[] = $featured ? 1 : 0;
            }
            
            $sql .= " ORDER BY featured DESC, published_at DESC";
            
            $stmt = $this->db->query($sql, $params);
            return $this->db->fetchAll($stmt);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    // ดึงข้อมูลข่าวสารตาม ID
    public function getNewsById($newsId) {
        try {
            $sql = "SELECT * FROM news WHERE news_id = ? AND status = 'published'";
            $stmt = $this->db->query($sql, [$newsId]);
            $news = $this->db->fetch($stmt);
            
            if ($news) {
                // เพิ่มจำนวนการดู
                $this->incrementNewsViews($newsId);
            }
            
            return $news;
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    // เพิ่มจำนวนการดูข่าว
    private function incrementNewsViews($newsId) {
        try {
            $sql = "UPDATE news SET views = views + 1 WHERE news_id = ?";
            $this->db->query($sql, [$newsId]);
        } catch (Exception $e) {
            // ไม่ต้องทำอะไรถ้าอัพเดทไม่สำเร็จ
        }
    }
    
    // ดึงข้อมูลกิจกรรม
    public function getEvents($status = null, $limit = 10) {
        try {
            $sql = "SELECT TOP (?) event_id, event_name, event_description, event_type,
                           start_date, end_date, max_participants, current_participants, status
                    FROM events";
            
            $params = [$limit];
            
            if ($status) {
                $sql .= " WHERE status = ?";
                $params[] = $status;
            }
            
            $sql .= " ORDER BY start_date DESC";
            
            $stmt = $this->db->query($sql, $params);
            return $this->db->fetchAll($stmt);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    // ดึงการตั้งค่าระบบ
    public function getSystemSetting($key, $default = null) {
        try {
            $sql = "SELECT setting_value, setting_type FROM system_settings WHERE setting_key = ?";
            $stmt = $this->db->query($sql, [$key]);
            $result = $this->db->fetch($stmt);
            
            if ($result) {
                $value = $result['setting_value'];
                switch ($result['setting_type']) {
                    case 'int':
                        return (int)$value;
                    case 'bool':
                        return $value === 'true' || $value === '1';
                    case 'json':
                        return json_decode($value, true);
                    default:
                        return $value;
                }
            }
            
            return $default;
            
        } catch (Exception $e) {
            return $default;
        }
    }
    
    // ดึง ID ของ record ที่เพิ่งสร้าง (สำหรับ SQL Server)
    private function getLastInsertId() {
        try {
            $sql = "SELECT SCOPE_IDENTITY() as last_id";
            $stmt = $this->db->query($sql);
            $result = $this->db->fetch($stmt);
            return $result['last_id'];
        } catch (Exception $e) {
            return null;
        }
    }
    
    public function __destruct() {
        if ($this->db) {
            $this->db->close();
        }
    }
}
?>
