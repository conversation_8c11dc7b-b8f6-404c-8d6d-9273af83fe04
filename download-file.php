<?php
require_once 'classes/Game.php';

// ตรวจสอบ ID ของไฟล์ดาวน์โหลด
$downloadId = $_GET['id'] ?? null;

if (!$downloadId || !is_numeric($downloadId)) {
    header('Location: download.php?error=invalid_id');
    exit;
}

$game = new Game();

// ดึงข้อมูลไฟล์ดาวน์โหลด
$downloads = $game->getDownloads();
$download = null;

foreach ($downloads as $dl) {
    if ($dl['download_id'] == $downloadId) {
        $download = $dl;
        break;
    }
}

if (!$download) {
    header('Location: download.php?error=file_not_found');
    exit;
}

// เพิ่มจำนวนการดาวน์โหลด
$game->incrementDownloadCount($downloadId);

// บันทึกล็อกการดาวน์โหลด
$logData = [
    'download_id' => $downloadId,
    'platform' => $download['platform'],
    'version' => $download['version'],
    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'timestamp' => date('Y-m-d H:i:s')
];

// บันทึกล็อก (สามารถเพิ่มฟังก์ชันบันทึกล็อกในคลาส Game ได้)
error_log('Download: ' . json_encode($logData));

// เปลี่ยนเส้นทางไปยัง URL ดาวน์โหลดจริง
header('Location: ' . $download['download_url']);
exit;
?>
