<?php
// ทดสอบการเชื่อมต่อฐานข้อมูล
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>ทดสอบการเชื่อมต่อฐานข้อมูล</h2>";

// ตรวจสอบ extension ที่จำเป็น
echo "<h3>1. ตรวจสอบ PHP Extensions:</h3>";
echo "PDO: " . (extension_loaded('pdo') ? "✅ ติดตั้งแล้ว" : "❌ ไม่ได้ติดตั้ง") . "<br>";
echo "PDO_SQLSRV: " . (extension_loaded('pdo_sqlsrv') ? "✅ ติดตั้งแล้ว" : "❌ ไม่ได้ติดตั้ง") . "<br>";
echo "SQLSRV: " . (extension_loaded('sqlsrv') ? "✅ ติดตั้งแล้ว" : "❌ ไม่ได้ติดตั้ง") . "<br>";

echo "<h3>2. ทดสอบการเชื่อมต่อ:</h3>";

try {
    require_once 'config/database.php';
    
    echo "กำลังทดสอบการเชื่อมต่อ...<br>";
    
    $db = new Database();
    $conn = $db->getConnection();
    
    if ($conn) {
        echo "✅ เชื่อมต่อฐานข้อมูลสำเร็จ!<br>";
        
        // ทดสอบ query
        echo "<h3>3. ทดสอบ Query:</h3>";
        
        // ตรวจสอบตารางที่มีอยู่
        $sql = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'";
        $stmt = $db->query($sql);
        $tables = $db->fetchAll($stmt);
        
        echo "ตารางที่มีในฐานข้อมูล:<br>";
        if (empty($tables)) {
            echo "❌ ไม่พบตารางใดๆ ในฐานข้อมูล<br>";
            echo "<strong>กรุณารันสคริปต์ database/create_tables.sql ก่อน</strong><br>";
        } else {
            foreach ($tables as $table) {
                echo "- " . $table['TABLE_NAME'] . "<br>";
            }
        }
        
        $db->close();
    } else {
        echo "❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้<br>";
    }
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "<br>";
}

echo "<h3>4. ข้อมูลการตั้งค่า:</h3>";
echo "Host: " . DB_HOST . "<br>";
echo "Database: " . DB_NAME . "<br>";
echo "User: " . DB_USER . "<br>";
echo "Port: " . DB_PORT . "<br>";

echo "<h3>5. คำแนะนำ:</h3>";
echo "หากเชื่อมต่อไม่ได้:<br>";
echo "1. ตรวจสอบว่า SQL Server เปิดอยู่<br>";
echo "2. ตรวจสอบ username/password<br>";
echo "3. ตรวจสอบว่าติดตั้ง PHP SQL Server drivers แล้ว<br>";
echo "4. รันสคริปต์สร้างฐานข้อมูลและตาราง<br>";
?>
