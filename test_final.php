<?php
// ทดสอบขั้นสุดท้าย
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Sweet Cabal - Final Test</h1>";

echo "<h2>Step 1: Basic PHP Test</h2>";
echo "✅ PHP is working<br>";
echo "PHP Version: " . phpversion() . "<br>";

echo "<h2>Step 2: Constants Test</h2>";
try {
    require_once 'config/constants.php';
    echo "✅ Constants loaded<br>";
    echo "Site Name: " . SITE_NAME . "<br>";
    echo "Site Description: " . SITE_DESCRIPTION . "<br>";
} catch (Exception $e) {
    echo "❌ Constants error: " . $e->getMessage() . "<br>";
}

echo "<h2>Step 3: Database Test</h2>";
try {
    require_once 'config/database.php';
    echo "✅ Database config loaded<br>";
    
    $db = new Database();
    $conn = $db->getConnection();
    
    if ($conn) {
        echo "✅ Database connected<br>";
        $db->close();
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<h2>Step 4: Game Class Test</h2>";
try {
    require_once 'classes/Game.php';
    echo "✅ Game class loaded<br>";
    
    $game = new Game();
    echo "✅ Game class instantiated<br>";
    
    // Test with fallback data
    $servers = [];
    try {
        $servers = $game->getAllServers();
        echo "✅ Servers loaded from database: " . count($servers) . " servers<br>";
    } catch (Exception $e) {
        echo "⚠️ Using fallback server data<br>";
        $servers = [
            ['server_name' => 'Sweet Server', 'server_status' => 'online'],
            ['server_name' => 'Cabal Server', 'server_status' => 'online'],
            ['server_name' => 'Magic Server', 'server_status' => 'maintenance']
        ];
    }
    
} catch (Exception $e) {
    echo "❌ Game class error: " . $e->getMessage() . "<br>";
}

echo "<h2>Step 5: Header Include Test</h2>";
try {
    $page_title = "Test Page";
    $page_description = "Test Description";
    
    ob_start();
    require_once 'includes/header.php';
    $header_content = ob_get_clean();
    
    if (strlen($header_content) > 100) {
        echo "✅ Header included successfully (" . strlen($header_content) . " characters)<br>";
    } else {
        echo "⚠️ Header seems too short<br>";
    }
} catch (Exception $e) {
    echo "❌ Header include error: " . $e->getMessage() . "<br>";
}

echo "<h2>Step 6: Index.php Test</h2>";
try {
    ob_start();
    include 'index.php';
    $index_content = ob_get_clean();
    
    if (strlen($index_content) > 1000) {
        echo "✅ Index.php loads successfully (" . strlen($index_content) . " characters)<br>";
        echo "✅ Content appears to be complete<br>";
    } else {
        echo "⚠️ Index.php content seems incomplete (" . strlen($index_content) . " characters)<br>";
        echo "First 200 characters:<br>";
        echo "<pre>" . htmlspecialchars(substr($index_content, 0, 200)) . "</pre>";
    }
} catch (Exception $e) {
    echo "❌ Index.php error: " . $e->getMessage() . "<br>";
}

echo "<h2>Recommendations</h2>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";
echo "<strong>If you see errors above:</strong><br>";
echo "1. Run <a href='test_db.php'>Database Test</a> to check SQL Server connection<br>";
echo "2. Make sure all files are uploaded correctly<br>";
echo "3. Check that SQL Server is running and accessible<br>";
echo "4. Run the database creation scripts<br>";
echo "5. Check PHP extensions (sqlsrv, pdo_sqlsrv)<br>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 10px; margin: 10px 0;'>";
echo "<strong>If everything looks good:</strong><br>";
echo "1. Try <a href='index.php'>Main Page</a><br>";
echo "2. Try <a href='test_simple.php'>Simple Test Page</a><br>";
echo "3. The website should be working now!<br>";
echo "</div>";

echo "<hr>";
echo "<p><strong>Quick Links:</strong></p>";
echo "<a href='index.php' style='margin-right: 10px;'>Main Page</a>";
echo "<a href='test_simple.php' style='margin-right: 10px;'>Simple Test</a>";
echo "<a href='test_db.php' style='margin-right: 10px;'>Database Test</a>";
echo "<a href='debug.php' style='margin-right: 10px;'>Debug Info</a>";
?>
