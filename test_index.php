<?php
// ทดสอบ index.php หลังจากแก้ไข constants
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='th'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Test Index</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; margin: 10px 0; border-radius: 5px; }";
echo ".error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; margin: 10px 0; border-radius: 5px; }";
echo ".info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; margin: 10px 0; border-radius: 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>Index.php Test Results</h1>";

echo "<div class='info'>";
echo "<h3>Testing index.php loading...</h3>";
echo "</div>";

// Test 1: Constants
echo "<h2>1. Constants Test</h2>";
try {
    require_once 'config/constants.php';
    echo "<div class='success'>✅ Constants loaded successfully</div>";
    echo "<p>SITE_NAME: " . SITE_NAME . "</p>";
} catch (Exception $e) {
    echo "<div class='error'>❌ Constants error: " . $e->getMessage() . "</div>";
}

// Test 2: Database
echo "<h2>2. Database Test</h2>";
try {
    require_once 'config/database.php';
    echo "<div class='success'>✅ Database config loaded</div>";
    
    $db = new Database();
    $conn = $db->getConnection();
    if ($conn) {
        echo "<div class='success'>✅ Database connected</div>";
        $db->close();
    } else {
        echo "<div class='error'>❌ Database connection failed</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
}

// Test 3: Game Class
echo "<h2>3. Game Class Test</h2>";
try {
    require_once 'classes/Game.php';
    $game = new Game();
    echo "<div class='success'>✅ Game class loaded and instantiated</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ Game class error: " . $e->getMessage() . "</div>";
}

// Test 4: Index.php Content
echo "<h2>4. Index.php Content Test</h2>";
try {
    // Reset any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    ob_start();
    
    // Set required variables
    $page_title = 'หน้าหลัก';
    $page_description = 'Sweet Cabal - เกม MMORPG สุดมันส์';
    
    // Include index.php
    include 'index.php';
    
    $content = ob_get_clean();
    
    if (strlen($content) > 2000) {
        echo "<div class='success'>✅ Index.php generated content successfully (" . number_format(strlen($content)) . " characters)</div>";
        
        // Check for key elements
        if (strpos($content, 'Sweet Cabal') !== false) {
            echo "<div class='success'>✅ Contains site title</div>";
        }
        if (strpos($content, 'bootstrap') !== false) {
            echo "<div class='success'>✅ Contains Bootstrap CSS</div>";
        }
        if (strpos($content, 'navbar') !== false) {
            echo "<div class='success'>✅ Contains navigation</div>";
        }
        
    } else {
        echo "<div class='error'>❌ Index.php content seems incomplete (" . strlen($content) . " characters)</div>";
        echo "<h4>Content preview:</h4>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 200px; overflow: auto;'>";
        echo htmlspecialchars(substr($content, 0, 500));
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Index.php error: " . $e->getMessage() . "</div>";
}

echo "<h2>Final Result</h2>";
echo "<div class='info'>";
echo "<h3>🎉 Constants conflict has been resolved!</h3>";
echo "<p>The duplicate constants have been removed from database.php</p>";
echo "<p>Your website should now work properly.</p>";
echo "</div>";

echo "<h3>Quick Links:</h3>";
echo "<p>";
echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🏠 Main Page</a>";
echo "<a href='test_simple.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🧪 Simple Test</a>";
echo "<a href='test_db.php' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🗄️ Database Test</a>";
echo "</p>";

echo "</body>";
echo "</html>";
?>
