// Game-specific JavaScript functions

// Global variables
let isLoading = false;

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeGameFunctions();
    initializeForms();
    initializeServerStatus();
});

// Initialize game functions
function initializeGameFunctions() {
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add loading states to buttons
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function() {
            if (this.type === 'submit' && !isLoading) {
                addLoadingState(this);
            }
        });
    });
    
    // Initialize tooltips
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

// Initialize forms
function initializeForms() {
    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Register form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    
    // Password strength indicator
    const passwordInput = document.getElementById('regPassword');
    if (passwordInput) {
        passwordInput.addEventListener('input', checkPasswordStrength);
    }
    
    // Confirm password validation
    const confirmPasswordInput = document.getElementById('confirmPassword');
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', validatePasswordMatch);
    }
}

// Handle login form submission
async function handleLogin(e) {
    e.preventDefault();
    
    if (isLoading) return;
    
    const form = e.target;
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    try {
        isLoading = true;
        addLoadingState(submitButton);
        
        const response = await fetch('auth/login.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('success', result.message);
            setTimeout(() => {
                if (result.redirect) {
                    window.location.href = result.redirect;
                } else {
                    window.location.reload();
                }
            }, 1000);
        } else {
            showAlert('error', result.message);
        }
        
    } catch (error) {
        console.error('Login error:', error);
        showAlert('error', 'เกิดข้อผิดพลาดในการเชื่อมต่อ กรุณาลองใหม่อีกครั้ง');
    } finally {
        isLoading = false;
        removeLoadingState(submitButton);
    }
}

// Handle register form submission
async function handleRegister(e) {
    e.preventDefault();
    
    if (isLoading) return;
    
    const form = e.target;
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Client-side validation
    if (!validateRegistrationForm(form)) {
        return;
    }
    
    try {
        isLoading = true;
        addLoadingState(submitButton);
        
        const response = await fetch('auth/register.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('success', result.message);
            setTimeout(() => {
                if (result.redirect) {
                    window.location.href = result.redirect;
                } else {
                    window.location.reload();
                }
            }, 2000);
        } else {
            showAlert('error', result.message);
        }
        
    } catch (error) {
        console.error('Registration error:', error);
        showAlert('error', 'เกิดข้อผิดพลาดในการเชื่อมต่อ กรุณาลองใหม่อีกครั้ง');
    } finally {
        isLoading = false;
        removeLoadingState(submitButton);
    }
}

// Validate registration form
function validateRegistrationForm(form) {
    const username = form.username.value.trim();
    const email = form.email.value.trim();
    const password = form.password.value;
    const confirmPassword = form.confirm_password.value;
    const agreeTerms = form.agree_terms.checked;
    
    // Username validation
    if (username.length < 3 || username.length > 20) {
        showAlert('error', 'ชื่อผู้ใช้ต้องมีความยาว 3-20 ตัวอักษร');
        return false;
    }
    
    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        showAlert('error', 'ชื่อผู้ใช้สามารถใช้ได้เฉพาะตัวอักษร ตัวเลข และ _ เท่านั้น');
        return false;
    }
    
    // Email validation
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        showAlert('error', 'รูปแบบอีเมลไม่ถูกต้อง');
        return false;
    }
    
    // Password validation
    if (password.length < 6) {
        showAlert('error', 'รหัสผ่านต้องมีความยาวอย่างน้อย 6 ตัวอักษร');
        return false;
    }
    
    if (!/^(?=.*[a-zA-Z])(?=.*\d)/.test(password)) {
        showAlert('error', 'รหัสผ่านต้องประกอบด้วยตัวอักษรและตัวเลข');
        return false;
    }
    
    // Confirm password validation
    if (password !== confirmPassword) {
        showAlert('error', 'รหัสผ่านและการยืนยันรหัสผ่านไม่ตรงกัน');
        return false;
    }
    
    // Terms agreement validation
    if (!agreeTerms) {
        showAlert('error', 'กรุณายอมรับข้อกำหนดการใช้งาน');
        return false;
    }
    
    return true;
}

// Check password strength
function checkPasswordStrength() {
    const password = this.value;
    const strengthIndicator = document.getElementById('passwordStrength');
    
    if (!strengthIndicator) return;
    
    let strength = 0;
    let strengthText = '';
    let strengthClass = '';
    
    if (password.length >= 6) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[^a-zA-Z\d]/.test(password)) strength++;
    
    switch (strength) {
        case 0:
        case 1:
            strengthText = 'อ่อนมาก';
            strengthClass = 'text-danger';
            break;
        case 2:
            strengthText = 'อ่อน';
            strengthClass = 'text-warning';
            break;
        case 3:
            strengthText = 'ปานกลาง';
            strengthClass = 'text-info';
            break;
        case 4:
            strengthText = 'แข็งแกร่ง';
            strengthClass = 'text-success';
            break;
        case 5:
            strengthText = 'แข็งแกร่งมาก';
            strengthClass = 'text-success fw-bold';
            break;
    }
    
    strengthIndicator.textContent = strengthText;
    strengthIndicator.className = `small ${strengthClass}`;
}

// Validate password match
function validatePasswordMatch() {
    const password = document.getElementById('regPassword').value;
    const confirmPassword = this.value;
    const matchIndicator = document.getElementById('passwordMatch');
    
    if (!matchIndicator) return;
    
    if (confirmPassword === '') {
        matchIndicator.textContent = '';
        return;
    }
    
    if (password === confirmPassword) {
        matchIndicator.textContent = 'รหัสผ่านตรงกัน';
        matchIndicator.className = 'small text-success';
    } else {
        matchIndicator.textContent = 'รหัสผ่านไม่ตรงกัน';
        matchIndicator.className = 'small text-danger';
    }
}

// Initialize server status updates
function initializeServerStatus() {
    // Update server status every 30 seconds
    setInterval(updateServerStatus, 30000);
}

// Update server status
async function updateServerStatus() {
    try {
        const response = await fetch('api/server-status.php');
        const servers = await response.json();
        
        servers.forEach(server => {
            updateServerCard(server);
        });
        
    } catch (error) {
        console.error('Failed to update server status:', error);
    }
}

// Update individual server card
function updateServerCard(server) {
    const serverCard = document.querySelector(`[data-server-id="${server.server_id}"]`);
    if (!serverCard) return;
    
    const statusElement = serverCard.querySelector('.server-status');
    const playersElement = serverCard.querySelector('.server-players');
    const progressBar = serverCard.querySelector('.progress-bar');
    
    if (statusElement) {
        statusElement.textContent = getServerStatusText(server.server_status);
        statusElement.className = `server-status ${getServerStatusClass(server.server_status)}`;
    }
    
    if (playersElement && server.server_status === 'online') {
        playersElement.textContent = `${server.current_players} / ${server.max_players}`;
    }
    
    if (progressBar && server.server_status === 'online') {
        const percentage = (server.current_players / server.max_players) * 100;
        progressBar.style.width = `${percentage}%`;
    }
}

// Get server status text
function getServerStatusText(status) {
    switch (status) {
        case 'online': return 'ออนไลน์';
        case 'maintenance': return 'ปรับปรุง';
        case 'offline': return 'ออฟไลน์';
        default: return 'ไม่ทราบสถานะ';
    }
}

// Get server status CSS class
function getServerStatusClass(status) {
    switch (status) {
        case 'online': return 'server-online';
        case 'maintenance': return 'server-maintenance';
        case 'offline': return 'server-offline';
        default: return '';
    }
}

// Add loading state to button
function addLoadingState(button) {
    if (!button) return;
    
    button.disabled = true;
    button.dataset.originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังดำเนินการ...';
}

// Remove loading state from button
function removeLoadingState(button) {
    if (!button) return;
    
    button.disabled = false;
    if (button.dataset.originalText) {
        button.innerHTML = button.dataset.originalText;
        delete button.dataset.originalText;
    }
}

// Show alert message
function showAlert(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert-custom');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show alert-custom`;
    alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Format number with commas
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// Copy text to clipboard
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showAlert('success', 'คัดลอกเรียบร้อยแล้ว');
    } catch (err) {
        console.error('Failed to copy text: ', err);
        showAlert('error', 'ไม่สามารถคัดลอกได้');
    }
}
