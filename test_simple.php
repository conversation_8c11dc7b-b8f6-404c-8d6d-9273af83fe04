<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sweet Cabal - ทดสอบ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: #0a0a0a;
            color: #ffffff;
            font-family: 'Kanit', sans-serif;
        }
        .hero-section {
            min-height: 100vh;
            background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.5));
            display: flex;
            align-items: center;
        }
        .btn-primary {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="background: rgba(10, 10, 10, 0.95);">
        <div class="container">
            <a class="navbar-brand" href="#">
                <span class="fw-bold">Sweet Cabal</span>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">หน้าหลัก</a>
                <a class="nav-link" href="debug.php">Debug</a>
                <a class="nav-link" href="test_db.php">ทดสอบ DB</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-3 fw-bold mb-4" style="background: linear-gradient(45deg, #00d4ff, #ffd700); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        Sweet Cabal
                    </h1>
                    <p class="lead mb-4">เกม MMORPG สุดมันส์ที่จะพาคุณไปสู่โลกแห่งการผจญภัย</p>
                    <div class="mb-4">
                        <span class="badge bg-primary fs-6 p-2 me-2">
                            <i class="fas fa-sword me-2"></i>PvP สุดมันส์
                        </span>
                        <span class="badge bg-warning fs-6 p-2 me-2">
                            <i class="fas fa-users me-2"></i>Guild War
                        </span>
                        <span class="badge bg-danger fs-6 p-2">
                            <i class="fas fa-dragon me-2"></i>Boss Raid
                        </span>
                    </div>
                    <div>
                        <a href="#" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-play me-2"></i>เล่นเลย
                        </a>
                        <a href="#" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-video me-2"></i>ดูตัวอย่าง
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div style="font-size: 8rem; color: #00d4ff;">
                        <i class="fas fa-gamepad"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Server Status -->
    <section class="py-5" style="background: #1a1a1a;">
        <div class="container">
            <h2 class="text-center mb-5">
                <i class="fas fa-server text-primary me-2"></i>สถานะเซิร์ฟเวอร์
            </h2>
            <div class="row g-4">
                <div class="col-lg-4">
                    <div class="card bg-dark border-secondary">
                        <div class="card-body">
                            <h5 class="card-title">Sweet Server</h5>
                            <p class="text-success">
                                <i class="fas fa-circle me-1"></i>ออนไลน์
                            </p>
                            <div class="d-flex justify-content-between">
                                <span>ผู้เล่นออนไลน์</span>
                                <span>1,234 / 2,000</span>
                            </div>
                            <div class="progress mt-2">
                                <div class="progress-bar" style="width: 62%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card bg-dark border-secondary">
                        <div class="card-body">
                            <h5 class="card-title">Cabal Server</h5>
                            <p class="text-success">
                                <i class="fas fa-circle me-1"></i>ออนไลน์
                            </p>
                            <div class="d-flex justify-content-between">
                                <span>ผู้เล่นออนไลน์</span>
                                <span>856 / 2,000</span>
                            </div>
                            <div class="progress mt-2">
                                <div class="progress-bar" style="width: 43%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card bg-dark border-secondary">
                        <div class="card-body">
                            <h5 class="card-title">Magic Server</h5>
                            <p class="text-warning">
                                <i class="fas fa-tools me-1"></i>ปรับปรุง
                            </p>
                            <p class="text-muted">กำลังปรับปรุงระบบ</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Test Results -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">ผลการทดสอบ</h2>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>การทดสอบระบบ</h5>
                        <p>หากคุณเห็นหน้านี้แสดงว่า:</p>
                        <ul>
                            <li>✅ Web server ทำงานปกติ</li>
                            <li>✅ Bootstrap CSS โหลดได้</li>
                            <li>✅ Font Awesome โหลดได้</li>
                            <li>✅ ไฟล์ PHP ทำงานได้</li>
                        </ul>
                        <hr>
                        <p class="mb-0">
                            <strong>ขั้นตอนต่อไป:</strong>
                            <a href="test_db.php" class="btn btn-sm btn-primary ms-2">ทดสอบฐานข้อมูล</a>
                            <a href="debug.php" class="btn btn-sm btn-secondary ms-2">ตรวจสอบระบบ</a>
                            <a href="index.php" class="btn btn-sm btn-success ms-2">ลองหน้าหลัก</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
