<?php
header('Content-Type: application/json');
require_once '../classes/User.php';

// ตรวจสอบว่าเป็น POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// ตรวจสอบ CSRF token (ในอนาคต)
// if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
//     http_response_code(403);
//     echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
//     exit;
// }

try {
    // รับข้อมูลจากฟอร์ม
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);
    
    // ตรวจสอบข้อมูลที่จำเป็น
    if (empty($username) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'กรุณากรอกชื่อผู้ใช้และรหัสผ่าน']);
        exit;
    }
    
    // สร้าง User object และทำการเข้าสู่ระบบ
    $user = new User();
    $result = $user->login($username, $password, $rememberMe);
    
    if ($result['success']) {
        // เข้าสู่ระบบสำเร็จ
        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'redirect' => 'index.php'
        ]);
    } else {
        // เข้าสู่ระบบไม่สำเร็จ
        echo json_encode([
            'success' => false,
            'message' => $result['message']
        ]);
    }
    
} catch (Exception $e) {
    // จัดการข้อผิดพลาด
    error_log('Login error: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง'
    ]);
}
?>
