-- Sweet Cabal Database Schema for SQL Server
-- สร้างฐานข้อมูลและตารางสำหรับระบบเกม

-- สร้างฐานข้อมูล
IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = 'sweetcabal_db')
BEGIN
    CREATE DATABASE sweetcabal_db;
END
GO

USE sweetcabal_db;
GO

-- ตารางผู้ใช้งาน
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users (
        user_id INT IDENTITY(1,1) PRIMARY KEY,
        username NVARCHAR(50) NOT NULL UNIQUE,
        email NVARCHAR(100) NOT NULL UNIQUE,
        password_hash NVARCHAR(255) NOT NULL,
        first_name <PERSON>VA<PERSON><PERSON><PERSON>(50),
        last_name <PERSON><PERSON><PERSON><PERSON><PERSON>(50),
        phone NVARCHAR(20),
        birth_date DATE,
        gender NVARCHAR(10),
        status NVARCHAR(20) DEFAULT 'active', -- active, inactive, banned
        email_verified BIT DEFAULT 0,
        verification_token NVARCHAR(255),
        reset_token NVARCHAR(255),
        reset_token_expires DATETIME,
        last_login DATETIME,
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE()
    );
END
GO

-- ตารางเซิร์ฟเวอร์
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='servers' AND xtype='U')
BEGIN
    CREATE TABLE servers (
        server_id INT IDENTITY(1,1) PRIMARY KEY,
        server_name NVARCHAR(50) NOT NULL,
        server_description NVARCHAR(255),
        server_status NVARCHAR(20) DEFAULT 'online', -- online, maintenance, offline
        max_players INT DEFAULT 2000,
        current_players INT DEFAULT 0,
        server_ip NVARCHAR(50),
        server_port INT,
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE()
    );
END
GO

-- ตารางตัวละคร
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='characters' AND xtype='U')
BEGIN
    CREATE TABLE characters (
        character_id INT IDENTITY(1,1) PRIMARY KEY,
        user_id INT NOT NULL,
        server_id INT NOT NULL,
        character_name NVARCHAR(50) NOT NULL,
        character_class NVARCHAR(30) NOT NULL, -- Warrior, Mage, Archer, etc.
        level INT DEFAULT 1,
        experience BIGINT DEFAULT 0,
        hp INT DEFAULT 100,
        mp INT DEFAULT 50,
        strength INT DEFAULT 10,
        intelligence INT DEFAULT 10,
        dexterity INT DEFAULT 10,
        vitality INT DEFAULT 10,
        gold BIGINT DEFAULT 0,
        location_x FLOAT DEFAULT 0,
        location_y FLOAT DEFAULT 0,
        location_map NVARCHAR(50) DEFAULT 'starter_town',
        status NVARCHAR(20) DEFAULT 'active', -- active, deleted
        last_login DATETIME,
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (user_id) REFERENCES users(user_id),
        FOREIGN KEY (server_id) REFERENCES servers(server_id),
        UNIQUE(character_name, server_id)
    );
END
GO

-- ตารางกิลด์
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='guilds' AND xtype='U')
BEGIN
    CREATE TABLE guilds (
        guild_id INT IDENTITY(1,1) PRIMARY KEY,
        server_id INT NOT NULL,
        guild_name NVARCHAR(50) NOT NULL,
        guild_description NVARCHAR(500),
        guild_master_id INT NOT NULL,
        guild_level INT DEFAULT 1,
        guild_experience BIGINT DEFAULT 0,
        max_members INT DEFAULT 50,
        current_members INT DEFAULT 1,
        guild_gold BIGINT DEFAULT 0,
        guild_logo NVARCHAR(255),
        status NVARCHAR(20) DEFAULT 'active',
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (server_id) REFERENCES servers(server_id),
        FOREIGN KEY (guild_master_id) REFERENCES characters(character_id),
        UNIQUE(guild_name, server_id)
    );
END
GO

-- ตารางสมาชิกกิลด์
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='guild_members' AND xtype='U')
BEGIN
    CREATE TABLE guild_members (
        member_id INT IDENTITY(1,1) PRIMARY KEY,
        guild_id INT NOT NULL,
        character_id INT NOT NULL,
        rank NVARCHAR(30) DEFAULT 'member', -- master, officer, member
        joined_at DATETIME DEFAULT GETDATE(),
        contribution_points INT DEFAULT 0,
        FOREIGN KEY (guild_id) REFERENCES guilds(guild_id),
        FOREIGN KEY (character_id) REFERENCES characters(character_id),
        UNIQUE(guild_id, character_id)
    );
END
GO

-- ตารางข่าวสาร
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='news' AND xtype='U')
BEGIN
    CREATE TABLE news (
        news_id INT IDENTITY(1,1) PRIMARY KEY,
        title NVARCHAR(200) NOT NULL,
        content NTEXT NOT NULL,
        summary NVARCHAR(500),
        category NVARCHAR(50) DEFAULT 'general', -- update, event, maintenance, general
        featured BIT DEFAULT 0,
        image_url NVARCHAR(255),
        author_id INT,
        status NVARCHAR(20) DEFAULT 'published', -- draft, published, archived
        views INT DEFAULT 0,
        published_at DATETIME DEFAULT GETDATE(),
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (author_id) REFERENCES users(user_id)
    );
END
GO

-- ตารางกิจกรรม
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='events' AND xtype='U')
BEGIN
    CREATE TABLE events (
        event_id INT IDENTITY(1,1) PRIMARY KEY,
        event_name NVARCHAR(100) NOT NULL,
        event_description NTEXT,
        event_type NVARCHAR(50), -- pvp, pve, special, seasonal
        start_date DATETIME NOT NULL,
        end_date DATETIME NOT NULL,
        rewards NTEXT, -- JSON format
        requirements NTEXT, -- JSON format
        max_participants INT,
        current_participants INT DEFAULT 0,
        status NVARCHAR(20) DEFAULT 'upcoming', -- upcoming, active, ended, cancelled
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE()
    );
END
GO

-- ตารางการดาวน์โหลด
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='downloads' AND xtype='U')
BEGIN
    CREATE TABLE downloads (
        download_id INT IDENTITY(1,1) PRIMARY KEY,
        platform NVARCHAR(20) NOT NULL, -- windows, macos, android, ios
        version NVARCHAR(20) NOT NULL,
        file_name NVARCHAR(100) NOT NULL,
        file_size BIGINT NOT NULL, -- in bytes
        download_url NVARCHAR(500) NOT NULL,
        changelog NTEXT,
        is_latest BIT DEFAULT 0,
        download_count INT DEFAULT 0,
        status NVARCHAR(20) DEFAULT 'active',
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE()
    );
END
GO

-- ตารางสถิติการเล่น
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='player_stats' AND xtype='U')
BEGIN
    CREATE TABLE player_stats (
        stat_id INT IDENTITY(1,1) PRIMARY KEY,
        character_id INT NOT NULL,
        total_playtime INT DEFAULT 0, -- in minutes
        monsters_killed INT DEFAULT 0,
        players_killed INT DEFAULT 0,
        deaths INT DEFAULT 0,
        quests_completed INT DEFAULT 0,
        items_crafted INT DEFAULT 0,
        gold_earned BIGINT DEFAULT 0,
        gold_spent BIGINT DEFAULT 0,
        last_updated DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (character_id) REFERENCES characters(character_id)
    );
END
GO

-- ตารางการตั้งค่าระบบ
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='system_settings' AND xtype='U')
BEGIN
    CREATE TABLE system_settings (
        setting_id INT IDENTITY(1,1) PRIMARY KEY,
        setting_key NVARCHAR(100) NOT NULL UNIQUE,
        setting_value NTEXT,
        setting_type NVARCHAR(20) DEFAULT 'string', -- string, int, bool, json
        description NVARCHAR(255),
        updated_at DATETIME DEFAULT GETDATE()
    );
END
GO

-- ตารางล็อกระบบ
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='system_logs' AND xtype='U')
BEGIN
    CREATE TABLE system_logs (
        log_id INT IDENTITY(1,1) PRIMARY KEY,
        log_type NVARCHAR(50) NOT NULL, -- login, logout, error, admin, game
        user_id INT,
        character_id INT,
        message NVARCHAR(500),
        details NTEXT,
        ip_address NVARCHAR(50),
        user_agent NVARCHAR(500),
        created_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (user_id) REFERENCES users(user_id),
        FOREIGN KEY (character_id) REFERENCES characters(character_id)
    );
END
GO
