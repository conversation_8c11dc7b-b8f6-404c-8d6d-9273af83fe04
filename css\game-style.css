/* Game-specific styles inspired by MU WEOS */

/* Dark theme variables */
:root {
    --game-bg-primary: #0a0a0a;
    --game-bg-secondary: #1a1a1a;
    --game-bg-tertiary: #2a2a2a;
    --game-accent-blue: #00d4ff;
    --game-accent-gold: #ffd700;
    --game-accent-purple: #8a2be2;
    --game-accent-red: #ff4444;
    --game-text-primary: #ffffff;
    --game-text-secondary: #cccccc;
    --game-text-muted: #888888;
    --game-border: #333333;
    --game-shadow: rgba(0, 0, 0, 0.5);
    --game-glow-blue: rgba(0, 212, 255, 0.3);
    --game-glow-gold: rgba(255, 215, 0, 0.3);
}

/* Body and background */
body {
    background: var(--game-bg-primary);
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(138, 43, 226, 0.1) 0%, transparent 50%);
    min-height: 100vh;
    color: var(--game-text-primary);
}

/* Navigation */
.game-navbar {
    background: rgba(10, 10, 10, 0.95) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--game-border);
    box-shadow: 0 2px 20px var(--game-shadow);
}

.game-navbar .navbar-brand .logo {
    width: 40px;
    height: 40px;
    filter: drop-shadow(0 0 10px var(--game-glow-blue));
}

.game-navbar .navbar-nav .nav-link {
    color: var(--game-text-secondary) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease;
    position: relative;
}

.game-navbar .navbar-nav .nav-link:hover,
.game-navbar .navbar-nav .nav-link.active {
    color: var(--game-accent-blue) !important;
    text-shadow: 0 0 10px var(--game-glow-blue);
}

.game-navbar .navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--game-accent-blue), transparent);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.game-navbar .navbar-nav .nav-link:hover::after,
.game-navbar .navbar-nav .nav-link.active::after {
    width: 80%;
}

/* Dropdown menus */
.dropdown-menu {
    background: var(--game-bg-secondary) !important;
    border: 1px solid var(--game-border) !important;
    box-shadow: 0 10px 30px var(--game-shadow) !important;
}

.dropdown-item {
    color: var(--game-text-secondary) !important;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: var(--game-bg-tertiary) !important;
    color: var(--game-accent-blue) !important;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(45deg, var(--game-accent-blue), #0099cc);
    border: none;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

.btn-outline-light {
    border-color: var(--game-border);
    color: var(--game-text-secondary);
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: var(--game-accent-blue);
    border-color: var(--game-accent-blue);
    color: white;
    box-shadow: 0 4px 15px var(--game-glow-blue);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.5)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 30% 70%, var(--game-glow-blue) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, var(--game-glow-gold) 0%, transparent 50%);
    opacity: 0.3;
    animation: heroGlow 8s ease-in-out infinite alternate;
}

@keyframes heroGlow {
    0% { opacity: 0.2; }
    100% { opacity: 0.4; }
}

.hero-content h1 {
    font-size: 4rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--game-accent-blue), var(--game-accent-gold));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px var(--game-glow-blue);
    margin-bottom: 1rem;
}

.hero-content .lead {
    font-size: 1.5rem;
    color: var(--game-text-secondary);
    margin-bottom: 2rem;
}

/* Game Cards */
.game-card {
    background: var(--game-bg-secondary);
    border: 1px solid var(--game-border);
    border-radius: 15px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.game-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: all 0.5s ease;
}

.game-card:hover::before {
    left: 100%;
}

.game-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px var(--game-shadow);
    border-color: var(--game-accent-blue);
}

/* Server Status */
.server-status {
    background: var(--game-bg-secondary);
    border: 1px solid var(--game-border);
    border-radius: 10px;
    padding: 1.5rem;
}

.server-online {
    color: #00ff88;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.server-maintenance {
    color: #ffaa00;
    text-shadow: 0 0 10px rgba(255, 170, 0, 0.5);
}

.server-offline {
    color: #ff4444;
    text-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
}

/* Progress bars */
.progress {
    background: var(--game-bg-tertiary);
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(45deg, var(--game-accent-blue), #0099cc);
    box-shadow: 0 0 10px var(--game-glow-blue);
}

/* Tables */
.table-dark {
    background: var(--game-bg-secondary);
    border-color: var(--game-border);
}

.table-dark th {
    background: var(--game-bg-tertiary);
    border-color: var(--game-border);
    color: var(--game-accent-blue);
    text-shadow: 0 0 10px var(--game-glow-blue);
}

.table-dark td {
    border-color: var(--game-border);
}

/* Modals */
.modal-content {
    background: var(--game-bg-secondary) !important;
    border: 1px solid var(--game-border) !important;
    box-shadow: 0 20px 60px var(--game-shadow) !important;
}

.modal-header {
    border-bottom-color: var(--game-border) !important;
}

.modal-footer {
    border-top-color: var(--game-border) !important;
}

/* Form controls */
.form-control {
    background: var(--game-bg-tertiary) !important;
    border: 1px solid var(--game-border) !important;
    color: var(--game-text-primary) !important;
    transition: all 0.3s ease;
}

.form-control:focus {
    background: var(--game-bg-tertiary) !important;
    border-color: var(--game-accent-blue) !important;
    box-shadow: 0 0 0 0.2rem var(--game-glow-blue) !important;
    color: var(--game-text-primary) !important;
}

.form-control::placeholder {
    color: var(--game-text-muted) !important;
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.floating {
    animation: float 6s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.pulsing {
    animation: pulse 2s ease-in-out infinite;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-content .lead {
        font-size: 1.2rem;
    }
    
    .game-card {
        margin-bottom: 2rem;
    }
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--game-bg-primary);
}

::-webkit-scrollbar-thumb {
    background: var(--game-accent-blue);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0099cc;
}
