<?php
require_once 'config/database.php';

class User {
    private $db;
    private $conn;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    // สมัครสมาชิกใหม่
    public function register($username, $email, $password, $firstName = '', $lastName = '') {
        try {
            // ตรวจสอบว่า username หรือ email ซ้ำหรือไม่
            if ($this->isUsernameExists($username)) {
                return ['success' => false, 'message' => 'ชื่อผู้ใช้นี้มีอยู่แล้ว'];
            }
            
            if ($this->isEmailExists($email)) {
                return ['success' => false, 'message' => 'อีเมลนี้มีอยู่แล้ว'];
            }
            
            // เข้ารหัสรหัสผ่าน
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
            $verificationToken = bin2hex(random_bytes(32));
            
            $sql = "INSERT INTO users (username, email, password_hash, first_name, last_name, verification_token) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->query($sql, [$username, $email, $passwordHash, $firstName, $lastName, $verificationToken]);
            
            if ($stmt) {
                // ส่งอีเมลยืนยัน (ในอนาคต)
                return [
                    'success' => true, 
                    'message' => 'สมัครสมาชิกสำเร็จ กรุณาตรวจสอบอีเมลเพื่อยืนยันบัญชี',
                    'verification_token' => $verificationToken
                ];
            } else {
                return ['success' => false, 'message' => 'เกิดข้อผิดพลาดในการสมัครสมาชิก'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()];
        }
    }
    
    // เข้าสู่ระบบ
    public function login($username, $password, $rememberMe = false) {
        try {
            $sql = "SELECT user_id, username, email, password_hash, status, email_verified 
                    FROM users 
                    WHERE username = ? OR email = ?";
            
            $stmt = $this->db->query($sql, [$username, $username]);
            $user = $this->db->fetch($stmt);
            
            if ($user && password_verify($password, $user['password_hash'])) {
                if ($user['status'] !== 'active') {
                    return ['success' => false, 'message' => 'บัญชีของคุณถูกระงับ'];
                }
                
                // อัพเดทเวลาเข้าสู่ระบบล่าสุด
                $this->updateLastLogin($user['user_id']);
                
                // สร้าง session
                session_start();
                $_SESSION['user_id'] = $user['user_id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['email_verified'] = $user['email_verified'];
                
                // จดจำการเข้าสู่ระบบ
                if ($rememberMe) {
                    $token = bin2hex(random_bytes(32));
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 วัน
                    // บันทึก token ในฐานข้อมูล (ในอนาคต)
                }
                
                // บันทึกล็อก
                $this->logActivity($user['user_id'], 'login', 'User logged in successfully');
                
                return [
                    'success' => true, 
                    'message' => 'เข้าสู่ระบบสำเร็จ',
                    'user' => [
                        'user_id' => $user['user_id'],
                        'username' => $user['username'],
                        'email' => $user['email']
                    ]
                ];
            } else {
                return ['success' => false, 'message' => 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()];
        }
    }
    
    // ออกจากระบบ
    public function logout() {
        session_start();
        
        // บันทึกล็อก
        if (isset($_SESSION['user_id'])) {
            $this->logActivity($_SESSION['user_id'], 'logout', 'User logged out');
        }
        
        // ลบ session
        session_destroy();
        
        // ลบ remember cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
        
        return ['success' => true, 'message' => 'ออกจากระบบสำเร็จ'];
    }
    
    // ตรวจสอบว่า username มีอยู่แล้วหรือไม่
    private function isUsernameExists($username) {
        $sql = "SELECT COUNT(*) as count FROM users WHERE username = ?";
        $stmt = $this->db->query($sql, [$username]);
        $result = $this->db->fetch($stmt);
        return $result['count'] > 0;
    }
    
    // ตรวจสอบว่า email มีอยู่แล้วหรือไม่
    private function isEmailExists($email) {
        $sql = "SELECT COUNT(*) as count FROM users WHERE email = ?";
        $stmt = $this->db->query($sql, [$email]);
        $result = $this->db->fetch($stmt);
        return $result['count'] > 0;
    }
    
    // อัพเดทเวลาเข้าสู่ระบบล่าสุด
    private function updateLastLogin($userId) {
        $sql = "UPDATE users SET last_login = GETDATE() WHERE user_id = ?";
        $this->db->query($sql, [$userId]);
    }
    
    // ดึงข้อมูลผู้ใช้
    public function getUserById($userId) {
        $sql = "SELECT user_id, username, email, first_name, last_name, phone, birth_date, 
                       gender, status, email_verified, last_login, created_at 
                FROM users WHERE user_id = ?";
        $stmt = $this->db->query($sql, [$userId]);
        return $this->db->fetch($stmt);
    }
    
    // อัพเดทข้อมูลผู้ใช้
    public function updateProfile($userId, $data) {
        try {
            $sql = "UPDATE users SET 
                    first_name = ?, 
                    last_name = ?, 
                    phone = ?, 
                    birth_date = ?, 
                    gender = ?, 
                    updated_at = GETDATE() 
                    WHERE user_id = ?";
            
            $stmt = $this->db->query($sql, [
                $data['first_name'],
                $data['last_name'],
                $data['phone'],
                $data['birth_date'],
                $data['gender'],
                $userId
            ]);
            
            if ($stmt) {
                $this->logActivity($userId, 'profile_update', 'User updated profile');
                return ['success' => true, 'message' => 'อัพเดทข้อมูลสำเร็จ'];
            } else {
                return ['success' => false, 'message' => 'เกิดข้อผิดพลาดในการอัพเดทข้อมูล'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()];
        }
    }
    
    // เปลี่ยนรหัสผ่าน
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            // ตรวจสอบรหัสผ่านปัจจุบัน
            $sql = "SELECT password_hash FROM users WHERE user_id = ?";
            $stmt = $this->db->query($sql, [$userId]);
            $user = $this->db->fetch($stmt);
            
            if (!$user || !password_verify($currentPassword, $user['password_hash'])) {
                return ['success' => false, 'message' => 'รหัสผ่านปัจจุบันไม่ถูกต้อง'];
            }
            
            // อัพเดทรหัสผ่านใหม่
            $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
            $sql = "UPDATE users SET password_hash = ?, updated_at = GETDATE() WHERE user_id = ?";
            $stmt = $this->db->query($sql, [$newPasswordHash, $userId]);
            
            if ($stmt) {
                $this->logActivity($userId, 'password_change', 'User changed password');
                return ['success' => true, 'message' => 'เปลี่ยนรหัสผ่านสำเร็จ'];
            } else {
                return ['success' => false, 'message' => 'เกิดข้อผิดพลาดในการเปลี่ยนรหัสผ่าน'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()];
        }
    }
    
    // บันทึกกิจกรรมของผู้ใช้
    private function logActivity($userId, $logType, $message, $details = null) {
        try {
            $sql = "INSERT INTO system_logs (log_type, user_id, message, details, ip_address, user_agent) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            
            $this->db->query($sql, [$logType, $userId, $message, $details, $ipAddress, $userAgent]);
        } catch (Exception $e) {
            // ไม่ต้องทำอะไรถ้าบันทึกล็อกไม่สำเร็จ
        }
    }
    
    // ยืนยันอีเมล
    public function verifyEmail($token) {
        try {
            $sql = "UPDATE users SET email_verified = 1, verification_token = NULL 
                    WHERE verification_token = ?";
            $stmt = $this->db->query($sql, [$token]);
            
            if ($stmt) {
                return ['success' => true, 'message' => 'ยืนยันอีเมลสำเร็จ'];
            } else {
                return ['success' => false, 'message' => 'โทเค็นไม่ถูกต้องหรือหมดอายุ'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()];
        }
    }
    
    // รีเซ็ตรหัสผ่าน
    public function requestPasswordReset($email) {
        try {
            $sql = "SELECT user_id FROM users WHERE email = ?";
            $stmt = $this->db->query($sql, [$email]);
            $user = $this->db->fetch($stmt);
            
            if (!$user) {
                return ['success' => false, 'message' => 'ไม่พบอีเมลนี้ในระบบ'];
            }
            
            $resetToken = bin2hex(random_bytes(32));
            $expiry = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            $sql = "UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE user_id = ?";
            $stmt = $this->db->query($sql, [$resetToken, $expiry, $user['user_id']]);
            
            if ($stmt) {
                // ส่งอีเมลรีเซ็ตรหัสผ่าน (ในอนาคต)
                return [
                    'success' => true, 
                    'message' => 'ส่งลิงก์รีเซ็ตรหัสผ่านไปยังอีเมลแล้ว',
                    'reset_token' => $resetToken
                ];
            } else {
                return ['success' => false, 'message' => 'เกิดข้อผิดพลาดในการส่งลิงก์รีเซ็ต'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()];
        }
    }
    
    public function __destruct() {
        if ($this->db) {
            $this->db->close();
        }
    }
}
?>
