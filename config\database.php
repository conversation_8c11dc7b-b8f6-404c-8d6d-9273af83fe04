<?php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'sweetcabal_db');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        return $this->conn;
    }
}

// Site Configuration
define('SITE_NAME', 'Sweet Cabal');
define('SITE_URL', 'http://localhost/sweetcabal');
define('SITE_DESCRIPTION', 'เกม MMORPG สุดมันส์ที่จะพาคุณไปสู่โลกแห่งการผจญภัย');

// Security
define('HASH_ALGO', PASSWORD_DEFAULT);
define('SESSION_LIFETIME', 3600); // 1 hour

// File Upload
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5242880); // 5MB

// Game Settings
define('MAX_LEVEL', 999);
define('STARTING_LEVEL', 1);
define('STARTING_EXP', 0);

// Server Status
define('SERVER_STATUS_ONLINE', 'online');
define('SERVER_STATUS_MAINTENANCE', 'maintenance');
define('SERVER_STATUS_OFFLINE', 'offline');
?>
