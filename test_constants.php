<?php
// ทดสอบ constants หลังจากแก้ไข
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Constants Test - After Fix</h1>";

echo "<h2>Step 1: Load Constants</h2>";
try {
    require_once 'config/constants.php';
    echo "✅ Constants loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ Constants error: " . $e->getMessage() . "<br>";
}

echo "<h2>Step 2: Load Database Config</h2>";
try {
    require_once 'config/database.php';
    echo "✅ Database config loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ Database config error: " . $e->getMessage() . "<br>";
}

echo "<h2>Step 3: Check Constants</h2>";
$constants_to_check = [
    'SITE_NAME',
    'SITE_URL', 
    'SITE_DESCRIPTION',
    'MAX_FILE_SIZE',
    'MIN_USERNAME_LENGTH',
    'MAX_USERNAME_LENGTH',
    'MIN_PASSWORD_LENGTH'
];

foreach ($constants_to_check as $constant) {
    if (defined($constant)) {
        echo "✅ {$constant}: " . constant($constant) . "<br>";
    } else {
        echo "❌ {$constant}: Not defined<br>";
    }
}

echo "<h2>Step 4: Test Index.php</h2>";
try {
    ob_start();
    include 'index.php';
    $content = ob_get_clean();
    
    if (strlen($content) > 1000) {
        echo "✅ Index.php loads successfully (" . strlen($content) . " characters)<br>";
    } else {
        echo "⚠️ Index.php content seems short (" . strlen($content) . " characters)<br>";
    }
} catch (Exception $e) {
    echo "❌ Index.php error: " . $e->getMessage() . "<br>";
}

echo "<h2>Result</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
echo "<strong>✅ Constants conflict fixed!</strong><br>";
echo "The duplicate constants have been removed from database.php<br>";
echo "All constants are now properly defined in constants.php<br>";
echo "</div>";

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<a href='index.php' style='margin-right: 10px; padding: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>Try Main Page</a>";
echo "<a href='test_db.php' style='margin-right: 10px; padding: 10px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>Test Database</a>";
echo "<a href='test_simple.php' style='padding: 10px; background: #17a2b8; color: white; text-decoration: none; border-radius: 5px;'>Simple Test</a>";
?>
