<?php
$page_title = 'จัดอันดับผู้เล่น';
$page_description = 'ดูอันดับผู้เล่นและกิลด์ที่แข็งแกร่งที่สุดใน Sweet Cabal';

require_once 'classes/Game.php';
$game = new Game();

// รับพารามิเตอร์
$rankingType = $_GET['type'] ?? 'level';
$serverId = $_GET['server'] ?? null;
$limit = 50;

// ดึงข้อมูลเซิร์ฟเวอร์
$servers = $game->getAllServers();

// ดึงข้อมูลอันดับตามประเภท
if ($rankingType === 'guild') {
    $rankings = $game->getGuildRanking($serverId, $limit);
} else {
    $rankings = $game->getPlayerRanking($rankingType, $serverId, $limit);
}

require_once 'includes/header.php';
?>

<!-- Ranking Section -->
<section class="py-5 mt-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5" data-aos="fade-up">
                <h1 class="display-4 fw-bold mb-3">จัดอันดับ</h1>
                <p class="lead">ดูอันดับผู้เล่นและกิลด์ที่แข็งแกร่งที่สุด</p>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12" data-aos="fade-up">
                <div class="game-card">
                    <form method="GET" class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label for="type" class="form-label">ประเภทอันดับ</label>
                            <select name="type" id="type" class="form-select">
                                <option value="level" <?php echo $rankingType === 'level' ? 'selected' : ''; ?>>
                                    อันดับเลเวล
                                </option>
                                <option value="pvp" <?php echo $rankingType === 'pvp' ? 'selected' : ''; ?>>
                                    อันดับ PvP
                                </option>
                                <option value="guild" <?php echo $rankingType === 'guild' ? 'selected' : ''; ?>>
                                    อันดับกิลด์
                                </option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="server" class="form-label">เซิร์ฟเวอร์</label>
                            <select name="server" id="server" class="form-select">
                                <option value="">ทุกเซิร์ฟเวอร์</option>
                                <?php foreach ($servers as $server): ?>
                                    <option value="<?php echo $server['server_id']; ?>" 
                                            <?php echo $serverId == $server['server_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($server['server_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>ค้นหา
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Rankings Table -->
        <div class="row">
            <div class="col-12" data-aos="fade-up">
                <div class="game-card">
                    <?php if ($rankingType === 'guild'): ?>
                        <h3 class="text-primary mb-4">
                            <i class="fas fa-shield-alt me-2"></i>อันดับกิลด์
                        </h3>
                        
                        <?php if (!empty($rankings)): ?>
                            <div class="table-responsive">
                                <table class="table table-dark table-hover">
                                    <thead>
                                        <tr>
                                            <th width="80">อันดับ</th>
                                            <th>ชื่อกิลด์</th>
                                            <th>หัวหน้ากิลด์</th>
                                            <th>เลเวล</th>
                                            <th>ประสบการณ์</th>
                                            <th>สมาชิก</th>
                                            <th>เซิร์ฟเวอร์</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($rankings as $index => $guild): ?>
                                            <tr>
                                                <td>
                                                    <?php if ($index < 3): ?>
                                                        <span class="badge bg-<?php echo ['warning', 'secondary', 'warning'][$index]; ?> fs-6">
                                                            <i class="fas fa-trophy"></i> <?php echo $guild['rank']; ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="fs-5 fw-bold"><?php echo $guild['rank']; ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong class="text-primary"><?php echo htmlspecialchars($guild['guild_name']); ?></strong>
                                                </td>
                                                <td><?php echo htmlspecialchars($guild['guild_master_name']); ?></td>
                                                <td>
                                                    <span class="badge bg-success"><?php echo number_format($guild['guild_level']); ?></span>
                                                </td>
                                                <td><?php echo number_format($guild['guild_experience']); ?></td>
                                                <td>
                                                    <span class="text-info"><?php echo $guild['current_members']; ?></span>
                                                </td>
                                                <td>
                                                    <small class="text-muted"><?php echo htmlspecialchars($guild['server_name']); ?></small>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">ไม่พบข้อมูลอันดับ</h5>
                                <p class="text-muted">ลองเปลี่ยนเงื่อนไขการค้นหา</p>
                            </div>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <h3 class="text-primary mb-4">
                            <i class="fas fa-user me-2"></i>
                            อันดับ<?php echo $rankingType === 'level' ? 'เลเวล' : 'PvP'; ?>
                        </h3>
                        
                        <?php if (!empty($rankings)): ?>
                            <div class="table-responsive">
                                <table class="table table-dark table-hover">
                                    <thead>
                                        <tr>
                                            <th width="80">อันดับ</th>
                                            <th>ชื่อตัวละคร</th>
                                            <th>คลาส</th>
                                            <th>เลเวล</th>
                                            <?php if ($rankingType === 'level'): ?>
                                                <th>ประสบการณ์</th>
                                            <?php else: ?>
                                                <th>ฆ่าผู้เล่น</th>
                                                <th>ถูกฆ่า</th>
                                                <th>K/D Ratio</th>
                                            <?php endif; ?>
                                            <th>เซิร์ฟเวอร์</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($rankings as $index => $player): ?>
                                            <tr>
                                                <td>
                                                    <?php if ($index < 3): ?>
                                                        <span class="badge bg-<?php echo ['warning', 'secondary', 'warning'][$index]; ?> fs-6">
                                                            <i class="fas fa-trophy"></i> <?php echo $player['rank']; ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="fs-5 fw-bold"><?php echo $player['rank']; ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong class="text-primary"><?php echo htmlspecialchars($player['character_name']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo htmlspecialchars($player['character_class']); ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success fs-6"><?php echo number_format($player['level']); ?></span>
                                                </td>
                                                <?php if ($rankingType === 'level'): ?>
                                                    <td><?php echo number_format($player['experience']); ?></td>
                                                <?php else: ?>
                                                    <td>
                                                        <span class="text-danger"><?php echo number_format($player['players_killed'] ?? 0); ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="text-warning"><?php echo number_format($player['deaths'] ?? 0); ?></span>
                                                    </td>
                                                    <td>
                                                        <?php 
                                                        $kills = $player['players_killed'] ?? 0;
                                                        $deaths = $player['deaths'] ?? 0;
                                                        $ratio = $deaths > 0 ? round($kills / $deaths, 2) : $kills;
                                                        ?>
                                                        <span class="text-success"><?php echo $ratio; ?></span>
                                                    </td>
                                                <?php endif; ?>
                                                <td>
                                                    <small class="text-muted"><?php echo htmlspecialchars($player['server_name']); ?></small>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">ไม่พบข้อมูลอันดับ</h5>
                                <p class="text-muted">ลองเปลี่ยนเงื่อนไขการค้นหา</p>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mt-5">
            <div class="col-12" data-aos="fade-up">
                <div class="game-card">
                    <h3 class="text-primary mb-4">
                        <i class="fas fa-chart-bar me-2"></i>สถิติรวม
                    </h3>
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <div class="stat-card">
                                <h4 class="text-warning">50,000+</h4>
                                <p class="mb-0">ผู้เล่นทั้งหมด</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stat-card">
                                <h4 class="text-success">1,200+</h4>
                                <p class="mb-0">กิลด์ที่ลงทะเบียน</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stat-card">
                                <h4 class="text-info">999</h4>
                                <p class="mb-0">เลเวลสูงสุด</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stat-card">
                                <h4 class="text-danger">24/7</h4>
                                <p class="mb-0">อัพเดทอันดับ</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hall of Fame -->
        <div class="row mt-5">
            <div class="col-12" data-aos="fade-up">
                <div class="game-card">
                    <h3 class="text-primary mb-4">
                        <i class="fas fa-crown me-2"></i>Hall of Fame
                    </h3>
                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            <div class="hall-of-fame-card">
                                <i class="fas fa-trophy fa-3x text-warning mb-3"></i>
                                <h5 class="text-warning">ผู้เล่นเลเวลสูงสุด</h5>
                                <p class="text-primary fw-bold">DragonSlayer999</p>
                                <p class="text-muted">Level 999 • Sweet Server</p>
                            </div>
                        </div>
                        <div class="col-md-4 text-center mb-4">
                            <div class="hall-of-fame-card">
                                <i class="fas fa-sword fa-3x text-danger mb-3"></i>
                                <h5 class="text-danger">นักรบ PvP ที่แข็งแกร่งที่สุด</h5>
                                <p class="text-primary fw-bold">ShadowKiller</p>
                                <p class="text-muted">K/D: 15.7 • Cabal Server</p>
                            </div>
                        </div>
                        <div class="col-md-4 text-center mb-4">
                            <div class="hall-of-fame-card">
                                <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                                <h5 class="text-success">กิลด์ที่แข็งแกร่งที่สุด</h5>
                                <p class="text-primary fw-bold">Legends</p>
                                <p class="text-muted">Level 50 • Sweet Server</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.stat-card {
    padding: 1rem;
    background: var(--game-bg-tertiary);
    border-radius: 10px;
    border: 1px solid var(--game-border);
}

.hall-of-fame-card {
    padding: 2rem 1rem;
    background: var(--game-bg-tertiary);
    border-radius: 15px;
    border: 1px solid var(--game-border);
    transition: all 0.3s ease;
}

.hall-of-fame-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px var(--game-shadow);
}
</style>

<?php require_once 'includes/footer.php'; ?>
